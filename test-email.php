<?php
require_once 'includes/config.php';
require_once 'includes/email.php';

// Check if this is a POST request to send test email
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_test'])) {
    $testEmail = $_POST['test_email'] ?? ADMIN_EMAIL;
    
    try {
        $emailManager = new EmailManager();
        $result = $emailManager->sendTestEmail($testEmail);
        
        if ($result) {
            $message = "✅ Test email sent successfully to: " . htmlspecialchars($testEmail);
            $messageType = "success";
        } else {
            $message = "❌ Failed to send test email. Please check your email configuration.";
            $messageType = "danger";
        }
    } catch (Exception $e) {
        $message = "❌ Error: " . $e->getMessage();
        $messageType = "danger";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Test - Event Booking System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="fas fa-envelope-circle-check me-2"></i>Email Configuration Test</h3>
                    </div>
                    <div class="card-body">
                        <?php if (isset($message)): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <h5>📧 Email Configuration Status</h5>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>SMTP Host:</span>
                                        <strong><?php echo SMTP_HOST; ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>SMTP Port:</span>
                                        <strong><?php echo SMTP_PORT; ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Username:</span>
                                        <strong><?php echo SMTP_USERNAME; ?></strong>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Encryption:</span>
                                        <strong><?php echo SMTP_ENCRYPTION; ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>From Email:</span>
                                        <strong><?php echo FROM_EMAIL; ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Password Set:</span>
                                        <strong><?php echo !empty(SMTP_PASSWORD) ? '✅ Yes' : '❌ No'; ?></strong>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <?php if (empty(SMTP_PASSWORD)): ?>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Gmail App Password Required</h6>
                            <p class="mb-2">To send emails, you need to set up a Gmail App Password:</p>
                            <ol class="mb-2">
                                <li>Go to your <a href="https://myaccount.google.com/" target="_blank">Google Account settings</a></li>
                                <li>Enable 2-factor authentication if not already enabled</li>
                                <li>Go to Security → 2-Step Verification → App passwords</li>
                                <li>Generate a new App Password for "Mail"</li>
                                <li>Copy the 16-character password</li>
                                <li>Add it to <code>includes/config.php</code> in the <code>SMTP_PASSWORD</code> constant</li>
                            </ol>
                            <p class="mb-0"><strong>Note:</strong> Use the App Password, not your regular Gmail password!</p>
                        </div>
                        <?php endif; ?>

                        <h5>🧪 Send Test Email</h5>
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="test_email" class="form-label">Test Email Address</label>
                                <input type="email" class="form-control" id="test_email" name="test_email" 
                                       value="<?php echo ADMIN_EMAIL; ?>" required>
                                <div class="form-text">Enter the email address where you want to receive the test email</div>
                            </div>
                            <button type="submit" name="send_test" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Send Test Email
                            </button>
                        </form>

                        <hr class="my-4">

                        <h5>📋 Email Features Available</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Booking confirmations</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Contact form messages</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Test email functionality</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>HTML email templates</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Gmail SMTP integration</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Error logging</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-4">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
