<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Profile';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$error = '';
$success = '';

// Get user details
$user = $userManager->getUserById($userId);

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $error = 'Invalid security token.';
    } else {
        $firstName = sanitizeInput($_POST['first_name']);
        $lastName = sanitizeInput($_POST['last_name']);
        $phone = sanitizeInput($_POST['phone']);
        $address = sanitizeInput($_POST['address']);
        
        // Validation
        if (empty($firstName) || empty($lastName)) {
            $error = 'First name and last name are required.';
        } else {
            $updateData = [
                'first_name' => $firstName,
                'last_name' => $lastName,
                'phone' => $phone,
                'address' => $address
            ];
            
            if ($userManager->updateProfile($userId, $updateData)) {
                // Update session data
                $_SESSION['user_name'] = $firstName . ' ' . $lastName;
                $success = 'Profile updated successfully.';
                
                // Refresh user data
                $user = $userManager->getUserById($userId);
            } else {
                $error = 'Failed to update profile. Please try again.';
            }
        }
    }
}
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item active">Profile</li>
                </ol>
            </nav>
            <h1><i class="fas fa-user-edit me-2"></i>My Profile</h1>
        </div>
    </div>

    <div class="row">
        <!-- Profile Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" id="profileForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?php echo htmlspecialchars($user->first_name); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?php echo htmlspecialchars($user->last_name); ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" 
                                       value="<?php echo htmlspecialchars($user->username); ?>" readonly>
                                <div class="form-text">Username cannot be changed</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" 
                                       value="<?php echo htmlspecialchars($user->email); ?>" readonly>
                                <div class="form-text">Email cannot be changed</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($user->phone); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($user->address); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Account Created</label>
                            <input type="text" class="form-control" 
                                   value="<?php echo formatDate($user->created_at); ?>" readonly>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                        <a href="dashboard.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </form>
                </div>
            </div>
        </div>

        <!-- Profile Summary -->
        <div class="col-lg-4">
            <!-- Account Summary -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Account Summary</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px; font-size: 32px;">
                            <?php echo strtoupper(substr($user->first_name, 0, 1) . substr($user->last_name, 0, 1)); ?>
                        </div>
                        <h5 class="mt-2 mb-0"><?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?></h5>
                        <small class="text-muted">@<?php echo htmlspecialchars($user->username); ?></small>
                    </div>
                    
                    <div class="border-top pt-3">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary"><?php echo count($bookingManager->getUserBookings($userId)); ?></h4>
                                <small class="text-muted">Total Bookings</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning"><?php echo $cartManager->getCartCount($userId); ?></h4>
                                <small class="text-muted">Cart Items</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Settings -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Account Settings</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="changePassword()">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                        <button class="btn btn-outline-info" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>Export My Data
                        </button>
                        <button class="btn btn-outline-warning" onclick="manageNotifications()">
                            <i class="fas fa-bell me-2"></i>Notification Settings
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteAccount()">
                            <i class="fas fa-trash me-2"></i>Delete Account
                        </button>
                    </div>
                </div>
            </div>

            <!-- Privacy Notice -->
            <div class="card shadow">
                <div class="card-body">
                    <h6><i class="fas fa-shield-alt me-2"></i>Privacy & Security</h6>
                    <p class="small text-muted mb-2">
                        Your personal information is protected and will never be shared with third parties without your consent.
                    </p>
                    <p class="small text-muted mb-0">
                        <a href="#" class="text-decoration-none">Privacy Policy</a> | 
                        <a href="#" class="text-decoration-none">Terms of Service</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" required minlength="6">
                    </div>
                    <div class="mb-3">
                        <label for="confirm_new_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_new_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitPasswordChange()">Change Password</button>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.getElementById('profileForm').addEventListener('submit', function(e) {
    if (!validateForm('profileForm')) {
        e.preventDefault();
        showAlert('danger', 'Please fill in all required fields.');
    }
});

function changePassword() {
    new bootstrap.Modal(document.getElementById('changePasswordModal')).show();
}

function submitPasswordChange() {
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_new_password').value;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        showAlert('danger', 'Please fill in all password fields.');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showAlert('danger', 'New passwords do not match.');
        return;
    }
    
    if (newPassword.length < 6) {
        showAlert('danger', 'New password must be at least 6 characters long.');
        return;
    }
    
    // In a real implementation, this would make an AJAX call to change the password
    showAlert('info', 'Password change functionality would be implemented here');
    bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
}

function exportData() {
    showAlert('info', 'Data export functionality would be implemented here');
}

function manageNotifications() {
    showAlert('info', 'Notification settings would be implemented here');
}

function deleteAccount() {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
        showAlert('info', 'Account deletion functionality would be implemented here');
    }
}

// Real-time password confirmation validation
document.getElementById('confirm_new_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && newPassword !== confirmPassword) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
});
</script>

<?php include '../includes/footer.php'; ?>
