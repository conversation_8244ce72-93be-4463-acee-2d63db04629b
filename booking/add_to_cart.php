<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Please login to add items to cart']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Validate CSRF token
if (!validateCSRFToken($_POST['csrf_token'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

// Get and validate input
$eventId = isset($_POST['event_id']) ? (int)$_POST['event_id'] : 0;
$quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;

if (!$eventId || $quantity < 1) {
    echo json_encode(['success' => false, 'message' => 'Invalid event or quantity']);
    exit;
}

// Limit quantity to prevent abuse
if ($quantity > 10) {
    echo json_encode(['success' => false, 'message' => 'Maximum 10 tickets per event']);
    exit;
}

// Add to cart
$userId = $_SESSION['user_id'];
$result = $cartManager->addToCart($userId, $eventId, $quantity);

if ($result) {
    $cartCount = $cartManager->getCartCount($userId);
    echo json_encode([
        'success' => true, 
        'message' => 'Event added to cart successfully',
        'cart_count' => $cartCount
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to add event to cart. Event may be sold out or unavailable.']);
}
?>
