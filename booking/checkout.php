<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/email.php';

$pageTitle = 'Checkout';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$error = '';

// Check if coming from direct event booking or cart
$directEventId = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;
$directQuantity = isset($_GET['quantity']) ? (int)$_GET['quantity'] : 1;

if ($directEventId) {
    // Direct booking from event details
    $event = $eventManager->getEventById($directEventId);
    if (!$event || $event->available_tickets < $directQuantity) {
        setFlashMessage('danger', 'Event not available or insufficient tickets.');
        redirect('../events/details.php?id=' . $directEventId);
    }

    $checkoutItems = [(object)[
        'event_id' => $event->id,
        'title' => $event->title,
        'price' => $event->price,
        'quantity' => $directQuantity,
        'event_date' => $event->event_date,
        'event_time' => $event->event_time,
        'venue' => $event->venue
    ]];
    $total = $event->price * $directQuantity;
} else {
    // Checkout from cart
    $checkoutItems = $cartManager->getCartItems($userId);
    if (empty($checkoutItems)) {
        setFlashMessage('warning', 'Your cart is empty.');
        redirect('cart.php');
    }
    $total = $cartManager->getCartTotal($userId);
}

// Get user details for pre-filling form
$user = $userManager->getUserById($userId);

// Process checkout form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $error = 'Invalid security token.';
    } else {
        $attendeeName = sanitizeInput($_POST['attendee_name']);
        $attendeeEmail = sanitizeInput($_POST['attendee_email']);
        $attendeePhone = sanitizeInput($_POST['attendee_phone']);
        $specialRequirements = sanitizeInput($_POST['special_requirements']);
        $paymentMethod = sanitizeInput($_POST['payment_method']);

        // Validation
        if (empty($attendeeName) || empty($attendeeEmail)) {
            $error = 'Please fill in all required fields.';
        } elseif (!filter_var($attendeeEmail, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } else {
            // Process each booking
            $bookingIds = [];
            $allSuccess = true;

            foreach ($checkoutItems as $item) {
                $attendeeData = [
                    'name' => $attendeeName,
                    'email' => $attendeeEmail,
                    'phone' => $attendeePhone,
                    'special_requirements' => $specialRequirements
                ];

                $bookingId = $bookingManager->createBooking($userId, $item->event_id, $item->quantity, $attendeeData);

                if ($bookingId) {
                    $bookingIds[] = $bookingId;
                    // Confirm booking (simulate payment success)
                    $bookingManager->confirmBooking($bookingId);

                    // Send confirmation email
                    try {
                        $booking = $bookingManager->getBookingById($bookingId);
                        $event = $eventManager->getEventById($item->event_id);
                        $emailManager->sendBookingConfirmation($booking, $event, $user);
                    } catch (Exception $e) {
                        error_log("Failed to send booking confirmation email: " . $e->getMessage());
                        // Don't fail the booking if email fails
                    }
                } else {
                    $allSuccess = false;
                    break;
                }
            }

            if ($allSuccess) {
                // Clear cart if checkout was from cart
                if (!$directEventId) {
                    $cartManager->clearCart($userId);
                }

                // Redirect to confirmation page
                $bookingReference = $bookingManager->getBookingByReference(generateBookingReference())->booking_reference ?? $bookingIds[0];
                setFlashMessage('success', 'Booking confirmed successfully!');
                redirect('confirmation.php?ref=' . $bookingReference);
            } else {
                $error = 'Booking failed. Please try again.';
            }
        }
    }
}
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                    <?php if (!$directEventId): ?>
                    <li class="breadcrumb-item"><a href="cart.php">Cart</a></li>
                    <?php endif; ?>
                    <li class="breadcrumb-item active">Checkout</li>
                </ol>
            </nav>
            <h1><i class="fas fa-credit-card me-2"></i>Checkout</h1>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Checkout Form -->
        <div class="col-lg-8">
            <form method="POST" id="checkoutForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <!-- Attendee Information -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>Attendee Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="attendee_name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="attendee_name" name="attendee_name"
                                       data-field-name="Full Name"
                                       value="<?php echo isset($_POST['attendee_name']) ? htmlspecialchars($_POST['attendee_name']) : htmlspecialchars($user->first_name . ' ' . $user->last_name); ?>"
                                       required minlength="3" maxlength="100">
                                <div class="form-text">Enter your full name as it should appear on the booking</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="attendee_email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="attendee_email" name="attendee_email"
                                       data-field-name="Email Address"
                                       value="<?php echo isset($_POST['attendee_email']) ? htmlspecialchars($_POST['attendee_email']) : htmlspecialchars($user->email); ?>"
                                       required maxlength="255">
                                <div class="form-text">Booking confirmation will be sent to this email</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="attendee_phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="attendee_phone" name="attendee_phone"
                                       data-field-name="Phone Number"
                                       placeholder="+237651408682"
                                       value="<?php echo isset($_POST['attendee_phone']) ? htmlspecialchars($_POST['attendee_phone']) : htmlspecialchars($user->phone); ?>">
                                <div class="form-text">Optional - For event updates and notifications</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="special_requirements" class="form-label">Special Requirements</label>
                            <textarea class="form-control" id="special_requirements" name="special_requirements" rows="3"
                                      placeholder="Any special dietary requirements, accessibility needs, etc."><?php echo isset($_POST['special_requirements']) ? htmlspecialchars($_POST['special_requirements']) : ''; ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Demo Mode:</strong> This is a demonstration checkout. No actual payment will be processed.
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Payment Method *</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="payment_method" id="credit_card" value="credit_card" checked>
                                        <label class="form-check-label" for="credit_card">
                                            <i class="fab fa-cc-visa me-1"></i>
                                            <i class="fab fa-cc-mastercard me-1"></i>
                                            Credit Card
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="payment_method" id="paypal" value="paypal">
                                        <label class="form-check-label" for="paypal">
                                            <i class="fab fa-paypal me-1"></i>PayPal
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="payment_method" id="bank_transfer" value="bank_transfer">
                                        <label class="form-check-label" for="bank_transfer">
                                            <i class="fas fa-university me-1"></i>Bank Transfer
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="credit_card_fields">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="card_number" class="form-label">Card Number</label>
                                    <input type="text" class="form-control" id="card_number" placeholder="1234 5678 9012 3456" maxlength="19">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="cvv" class="form-label">CVV</label>
                                    <input type="text" class="form-control" id="cvv" placeholder="123" maxlength="4">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="expiry_month" class="form-label">Expiry Month</label>
                                    <select class="form-select" id="expiry_month">
                                        <option value="">Month</option>
                                        <?php for ($i = 1; $i <= 12; $i++): ?>
                                            <option value="<?php echo sprintf('%02d', $i); ?>"><?php echo sprintf('%02d', $i); ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="expiry_year" class="form-label">Expiry Year</label>
                                    <select class="form-select" id="expiry_year">
                                        <option value="">Year</option>
                                        <?php for ($i = date('Y'); $i <= date('Y') + 10; $i++): ?>
                                            <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="card shadow mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="text-decoration-none">Terms and Conditions</a> and
                                <a href="#" class="text-decoration-none">Privacy Policy</a> *
                            </label>
                        </div>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="marketing">
                            <label class="form-check-label" for="marketing">
                                I would like to receive marketing emails about future events
                            </label>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-success btn-lg w-100">
                    <i class="fas fa-lock me-2"></i>Complete Booking - <?php echo formatCurrency($total); ?>
                </button>
            </form>
        </div>

        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="card shadow sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Order Summary</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($checkoutItems as $item): ?>
                    <div class="border-bottom pb-3 mb-3">
                        <h6 class="mb-1"><?php echo htmlspecialchars($item->title); ?></h6>
                        <p class="text-muted small mb-1">
                            <i class="fas fa-calendar me-1"></i><?php echo formatDate($item->event_date); ?> at <?php echo formatTime($item->event_time); ?>
                        </p>
                        <p class="text-muted small mb-1">
                            <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($item->venue); ?>
                        </p>
                        <div class="d-flex justify-content-between">
                            <span><?php echo $item->quantity; ?> × <?php echo formatCurrency($item->price); ?></span>
                            <strong><?php echo formatCurrency($item->price * $item->quantity); ?></strong>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span><?php echo formatCurrency($total); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Service Fee:</span>
                        <span><?php echo formatCurrency(0); ?></span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong class="text-success"><?php echo formatCurrency($total); ?></strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Wait for DOM and all scripts to load
document.addEventListener('DOMContentLoaded', function() {
    // Setup real-time validation if function exists
    if (typeof setupRealTimeValidation === 'function') {
        setupRealTimeValidation('checkoutForm');
    }

    // Initialize checkout form functionality
    initializeCheckoutForm();
});

// Initialize checkout form functionality
function initializeCheckoutForm() {
    // Format card number input with validation
    const cardNumberInput = document.getElementById('card_number');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;

            // Basic card validation
            if (value.length >= 13) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else if (value.length > 0) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
            }
        });
    }

    // CVV validation
    const cvvInput = document.getElementById('cvv');
    if (cvvInput) {
        cvvInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9]/gi, '');
            e.target.value = value;

            if (value.length >= 3) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else if (value.length > 0) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
            }
        });
    }

    // Show/hide payment fields based on selection
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const creditCardFields = document.getElementById('credit_card_fields');
            const cardInputs = creditCardFields.querySelectorAll('input, select');

            if (this.value === 'credit_card') {
                creditCardFields.style.display = 'block';
                // Make card fields required
                cardInputs.forEach(input => {
                    if (['card_number', 'cvv', 'expiry_month', 'expiry_year'].includes(input.id)) {
                        input.setAttribute('required', 'required');
                    }
                });
            } else {
                creditCardFields.style.display = 'none';
                // Remove required from card fields
                cardInputs.forEach(input => {
                    input.removeAttribute('required');
                    input.classList.remove('is-invalid', 'is-valid');
                });
            }
        });
    });

    // Enhanced form validation
    const checkoutForm = document.getElementById('checkoutForm');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(e) {
            const paymentMethodChecked = document.querySelector('input[name="payment_method"]:checked');
            const termsCheckbox = document.getElementById('terms');

            if (!paymentMethodChecked) {
                e.preventDefault();
                showAlertSafe('danger', 'Please select a payment method.');
                return;
            }

            const paymentMethod = paymentMethodChecked.value;
            const termsChecked = termsCheckbox ? termsCheckbox.checked : false;

            // Use fallback validation if main function not available
            if (typeof validateForm === 'function') {
                if (!validateForm('checkoutForm')) {
                    e.preventDefault();
                    return;
                }
            } else {
                // Basic fallback validation
                const requiredFields = this.querySelectorAll('input[required], select[required], textarea[required]');
                let hasErrors = false;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        hasErrors = true;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (hasErrors) {
                    e.preventDefault();
                    showAlertSafe('danger', 'Please fill in all required fields.');
                    return;
                }
            }

            if (!termsChecked) {
                e.preventDefault();
                showAlertSafe('danger', 'Please accept the Terms and Conditions to proceed.');
                if (termsCheckbox) termsCheckbox.focus();
                return;
            }

            // Additional validation for credit card
            if (paymentMethod === 'credit_card') {
                const cardNumberField = document.getElementById('card_number');
                const cvvField = document.getElementById('cvv');
                const expiryMonthField = document.getElementById('expiry_month');
                const expiryYearField = document.getElementById('expiry_year');

                const cardNumber = cardNumberField ? cardNumberField.value.replace(/\s/g, '') : '';
                const cvv = cvvField ? cvvField.value : '';
                const expiryMonth = expiryMonthField ? expiryMonthField.value : '';
                const expiryYear = expiryYearField ? expiryYearField.value : '';

                if (cardNumber.length < 13 || cardNumber.length > 19) {
                    e.preventDefault();
                    showAlertSafe('danger', 'Please enter a valid card number.');
                    if (cardNumberField) cardNumberField.focus();
                    return;
                }

                if (cvv.length < 3 || cvv.length > 4) {
                    e.preventDefault();
                    showAlertSafe('danger', 'Please enter a valid CVV.');
                    if (cvvField) cvvField.focus();
                    return;
                }

                if (!expiryMonth || !expiryYear) {
                    e.preventDefault();
                    showAlertSafe('danger', 'Please select card expiry date.');
                    return;
                }

                // Check if card is not expired
                const currentDate = new Date();
                const expiryDate = new Date(expiryYear, expiryMonth - 1);
                if (expiryDate < currentDate) {
                    e.preventDefault();
                    showAlertSafe('danger', 'Card expiry date cannot be in the past.');
                    return;
                }
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing Booking...';
                submitBtn.disabled = true;

                // Show confirmation message
                showAlertSafe('info', 'Processing your booking... Please do not refresh the page.');

                // Re-enable button after 15 seconds as fallback
                setTimeout(function() {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 15000);
            }
        });
    }
}

// Safe alert function that works even if main showAlert is not available
function showAlertSafe(type, message) {
    if (typeof showAlert === 'function') {
        showAlert(type, message);
    } else {
        // Fallback to browser alert
        alert(message);
    }
}
</script>

<?php include '../includes/footer.php'; ?>
