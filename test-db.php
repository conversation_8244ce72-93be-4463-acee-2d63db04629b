<?php
/**
 * Database Connection Test Script
 * This script tests the database connection and displays system information
 */

// Load configuration
require_once 'includes/config.php';
require_once 'includes/functions.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">🧪 Event Booking System - Database Test</h3>
                    </div>
                    <div class="card-body">

                        <!-- Database Connection Test -->
                        <div class="mb-4">
                            <h5>📊 Database Connection</h5>
                            <?php
                            try {
                                $connection = $db->getConnection();
                                echo '<div class="alert alert-success">✅ Database connection successful!</div>';

                                // Test query
                                $stmt = $connection->query("SELECT COUNT(*) as count FROM events");
                                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                echo '<p><strong>Events in database:</strong> ' . $result['count'] . '</p>';

                                $stmt = $connection->query("SELECT COUNT(*) as count FROM users");
                                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                echo '<p><strong>Users in database:</strong> ' . $result['count'] . '</p>';

                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">❌ Database connection failed: ' . $e->getMessage() . '</div>';
                            }
                            ?>
                        </div>

                        <!-- Configuration Info -->
                        <div class="mb-4">
                            <h5>⚙️ Configuration</h5>
                            <table class="table table-sm">
                                <tr><td><strong>Database Host:</strong></td><td><?php echo DB_HOST; ?></td></tr>
                                <tr><td><strong>Database Name:</strong></td><td><?php echo DB_NAME; ?></td></tr>
                                <tr><td><strong>Database User:</strong></td><td><?php echo DB_USER; ?></td></tr>
                                <tr><td><strong>Site URL:</strong></td><td><?php echo SITE_URL; ?></td></tr>
                                <tr><td><strong>Site Name:</strong></td><td><?php echo SITE_NAME; ?></td></tr>
                            </table>
                        </div>

                        <!-- PHP Info -->
                        <div class="mb-4">
                            <h5>🐘 PHP Information</h5>
                            <table class="table table-sm">
                                <tr><td><strong>PHP Version:</strong></td><td><?php echo phpversion(); ?></td></tr>
                                <tr><td><strong>PDO MySQL:</strong></td><td><?php echo extension_loaded('pdo_mysql') ? '✅ Enabled' : '❌ Disabled'; ?></td></tr>
                                <tr><td><strong>MySQLi:</strong></td><td><?php echo extension_loaded('mysqli') ? '✅ Enabled' : '❌ Disabled'; ?></td></tr>
                                <tr><td><strong>Session Status:</strong></td><td><?php echo session_status() === PHP_SESSION_ACTIVE ? '✅ Active' : '❌ Inactive'; ?></td></tr>
                            </table>
                        </div>

                        <!-- Sample Events -->
                        <div class="mb-4">
                            <h5>🎫 Sample Events</h5>
                            <?php
                            try {
                                $events = $eventManager->getAllEvents(3);
                                if (!empty($events)) {
                                    echo '<div class="row">';
                                    foreach ($events as $event) {
                                        echo '<div class="col-md-4 mb-3">';
                                        echo '<div class="card">';
                                        echo '<div class="card-body">';
                                        echo '<h6 class="card-title">' . htmlspecialchars($event->title) . '</h6>';
                                        echo '<p class="card-text small">' . formatDate($event->event_date) . '</p>';
                                        echo '<p class="card-text small">' . formatCurrency($event->price) . '</p>';
                                        echo '</div>';
                                        echo '</div>';
                                        echo '</div>';
                                    }
                                    echo '</div>';
                                } else {
                                    echo '<div class="alert alert-warning">No events found in database.</div>';
                                }
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">Error loading events: ' . $e->getMessage() . '</div>';
                            }
                            ?>
                        </div>

                        <div class="text-center">
                            <a href="index.php" class="btn btn-primary">🏠 Go to Main Application</a>
                            <a href="admin/index.php" class="btn btn-secondary">👨‍💼 Admin Panel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
