<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Booking Management';

// Require admin access
requireAdmin();

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';
        $bookingId = intval($_POST['booking_id']);

        switch ($action) {
            case 'confirm':
                if ($bookingManager->confirmBooking($bookingId)) {
                    $message = 'Booking confirmed successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to confirm booking. Please try again.';
                    $messageType = 'danger';
                }
                break;

            case 'cancel':
                if ($bookingManager->cancelBooking($bookingId)) {
                    $message = 'Booking cancelled successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to cancel booking. Please try again.';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get filter parameters
$status = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Build query based on filters
$query = 'SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location,
                 u.first_name, u.last_name, u.email as user_email
          FROM bookings b
          JOIN events e ON b.event_id = e.id
          JOIN users u ON b.user_id = u.id';

$params = [];
$conditions = [];

if (!empty($status)) {
    $conditions[] = 'b.booking_status = :status';
    $params[':status'] = $status;
}

if (!empty($search)) {
    $conditions[] = '(b.booking_reference LIKE :search OR e.title LIKE :search OR
                     CONCAT(u.first_name, " ", u.last_name) LIKE :search OR
                     b.attendee_name LIKE :search)';
    $params[':search'] = "%$search%";
}

if (!empty($conditions)) {
    $query .= ' WHERE ' . implode(' AND ', $conditions);
}

$query .= ' ORDER BY b.created_at DESC';

$db->query($query);
foreach ($params as $param => $value) {
    $db->bind($param, $value);
}
$bookings = $db->resultset();

// Get statistics
$db->query('SELECT
    COUNT(*) as total_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN 1 ELSE 0 END) as confirmed_bookings,
    SUM(CASE WHEN booking_status = "pending" THEN 1 ELSE 0 END) as pending_bookings,
    SUM(CASE WHEN booking_status = "cancelled" THEN 1 ELSE 0 END) as cancelled_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN total_amount ELSE 0 END) as total_revenue
    FROM bookings');
$stats = $db->single();
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-ticket-alt me-2"></i>Booking Management</h1>
                    <p class="text-muted">Manage all bookings in the system</p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="reports.php" class="btn btn-primary">
                        <i class="fas fa-chart-bar me-2"></i>View Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $stats->total_bookings; ?></h3>
                            <p class="mb-0">Total Bookings</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ticket-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $stats->confirmed_bookings; ?></h3>
                            <p class="mb-0">Confirmed</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $stats->pending_bookings; ?></h3>
                            <p class="mb-0">Pending</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo formatCurrency($stats->total_revenue); ?></h3>
                            <p class="mb-0">Total Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Booking ref, event, or customer name">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="confirmed" <?php echo $status === 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                        <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                    <a href="bookings.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Bookings List -->
    <div class="card shadow">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>All Bookings</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($bookings)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Booking Ref</th>
                                <th>Event</th>
                                <th>Customer</th>
                                <th>Attendee</th>
                                <th>Quantity</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($bookings as $booking): ?>
                            <tr>
                                <td>
                                    <code><?php echo $booking->booking_reference; ?></code>
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($booking->title); ?></h6>
                                        <small class="text-muted">
                                            <?php echo formatDate($booking->event_date); ?> at <?php echo formatTime($booking->event_time); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div><?php echo htmlspecialchars($booking->first_name . ' ' . $booking->last_name); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($booking->user_email); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div><?php echo htmlspecialchars($booking->attendee_name); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($booking->attendee_email); ?></small>
                                    </div>
                                </td>
                                <td><?php echo $booking->quantity; ?></td>
                                <td><?php echo formatCurrency($booking->total_amount); ?></td>
                                <td>
                                    <span class="badge bg-<?php
                                        echo $booking->booking_status === 'confirmed' ? 'success' :
                                             ($booking->booking_status === 'cancelled' ? 'danger' : 'warning');
                                    ?>">
                                        <?php echo ucfirst($booking->booking_status); ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($booking->created_at)); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="../booking/confirmation.php?ref=<?php echo $booking->booking_reference; ?>"
                                           class="btn btn-outline-info btn-sm" target="_blank">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($booking->booking_status === 'pending'): ?>
                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                onclick="confirmAction(<?php echo $booking->id; ?>, 'confirm', '<?php echo htmlspecialchars($booking->booking_reference); ?>')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <?php endif; ?>
                                        <?php if ($booking->booking_status !== 'cancelled'): ?>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="confirmAction(<?php echo $booking->id; ?>, 'cancel', '<?php echo htmlspecialchars($booking->booking_reference); ?>')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox text-muted" style="font-size: 64px;"></i>
                    <h4 class="mt-3 text-muted">No Bookings Found</h4>
                    <p class="text-muted">No bookings match your current filters.</p>
                    <a href="bookings.php" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>View All Bookings
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Action Confirmation Form -->
<form id="actionForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" id="actionType">
    <input type="hidden" name="booking_id" id="actionBookingId">
</form>

<script>
function confirmAction(bookingId, action, bookingRef) {
    const actionText = action === 'confirm' ? 'confirm' : 'cancel';
    const message = `Are you sure you want to ${actionText} booking ${bookingRef}?`;

    if (confirm(message)) {
        document.getElementById('actionType').value = action;
        document.getElementById('actionBookingId').value = bookingId;
        document.getElementById('actionForm').submit();
    }
}
</script>

<?php include '../includes/footer.php'; ?>
