<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'User Management';

// Require admin access
requireAdmin();

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';
        $userId = intval($_POST['user_id']);

        switch ($action) {
            case 'toggle_role':
                $newRole = $_POST['new_role'];
                $db->query('UPDATE users SET role = :role WHERE id = :id');
                $db->bind(':role', $newRole);
                $db->bind(':id', $userId);

                if ($db->execute()) {
                    $message = 'User role updated successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update user role. Please try again.';
                    $messageType = 'danger';
                }
                break;

            case 'delete_user':
                // Don't allow deleting the current admin user
                if ($userId == $_SESSION['user_id']) {
                    $message = 'You cannot delete your own account.';
                    $messageType = 'warning';
                } else {
                    $db->query('DELETE FROM users WHERE id = :id');
                    $db->bind(':id', $userId);

                    if ($db->execute()) {
                        $message = 'User deleted successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Failed to delete user. Please try again.';
                        $messageType = 'danger';
                    }
                }
                break;
        }
    }
}

// Get filter parameters
$role = $_GET['role'] ?? '';
$search = $_GET['search'] ?? '';

// Build query based on filters
$query = 'SELECT u.*,
                 COUNT(b.id) as total_bookings,
                 SUM(CASE WHEN b.booking_status = "confirmed" THEN b.total_amount ELSE 0 END) as total_spent,
                 MAX(b.created_at) as last_booking
          FROM users u
          LEFT JOIN bookings b ON u.id = b.user_id';

$params = [];
$conditions = [];

if (!empty($role)) {
    $conditions[] = 'u.role = :role';
    $params[':role'] = $role;
}

if (!empty($search)) {
    $conditions[] = '(u.first_name LIKE :search OR u.last_name LIKE :search OR
                     u.email LIKE :search OR u.username LIKE :search)';
    $params[':search'] = "%$search%";
}

if (!empty($conditions)) {
    $query .= ' WHERE ' . implode(' AND ', $conditions);
}

$query .= ' GROUP BY u.id ORDER BY u.created_at DESC';

$db->query($query);
foreach ($params as $param => $value) {
    $db->bind($param, $value);
}
$users = $db->resultset();

// Get statistics
$db->query('SELECT
    COUNT(*) as total_users,
    SUM(CASE WHEN role = "admin" THEN 1 ELSE 0 END) as admin_users,
    SUM(CASE WHEN role = "user" THEN 1 ELSE 0 END) as regular_users,
    SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_30_days
    FROM users');
$stats = $db->single();
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-users me-2"></i>User Management</h1>
                    <p class="text-muted">Manage all users in the system</p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="../auth/register.php" class="btn btn-primary" target="_blank">
                        <i class="fas fa-user-plus me-2"></i>Add New User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $stats->total_users; ?></h3>
                            <p class="mb-0">Total Users</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $stats->regular_users; ?></h3>
                            <p class="mb-0">Regular Users</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $stats->admin_users; ?></h3>
                            <p class="mb-0">Administrators</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-shield fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $stats->new_users_30_days; ?></h3>
                            <p class="mb-0">New (30 days)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Name, email, or username">
                </div>
                <div class="col-md-3">
                    <label for="role" class="form-label">Role</label>
                    <select class="form-control" id="role" name="role">
                        <option value="">All Roles</option>
                        <option value="user" <?php echo $role === 'user' ? 'selected' : ''; ?>>Regular User</option>
                        <option value="admin" <?php echo $role === 'admin' ? 'selected' : ''; ?>>Administrator</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users List -->
    <div class="card shadow">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>All Users</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($users)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Contact</th>
                                <th>Role</th>
                                <th>Bookings</th>
                                <th>Total Spent</th>
                                <th>Last Booking</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                             style="width: 40px; height: 40px;">
                                            <?php echo strtoupper(substr($user->first_name, 0, 1) . substr($user->last_name, 0, 1)); ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?></h6>
                                            <small class="text-muted">@<?php echo htmlspecialchars($user->username); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div><?php echo htmlspecialchars($user->email); ?></div>
                                        <?php if ($user->phone): ?>
                                            <small class="text-muted"><?php echo htmlspecialchars($user->phone); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $user->role === 'admin' ? 'danger' : 'primary'; ?>">
                                        <?php echo ucfirst($user->role); ?>
                                    </span>
                                </td>
                                <td><?php echo $user->total_bookings; ?></td>
                                <td><?php echo formatCurrency($user->total_spent); ?></td>
                                <td>
                                    <?php if ($user->last_booking): ?>
                                        <?php echo date('M j, Y', strtotime($user->last_booking)); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Never</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($user->created_at)); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <?php if ($user->id != $_SESSION['user_id']): ?>
                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                    onclick="toggleRole(<?php echo $user->id; ?>, '<?php echo $user->role; ?>', '<?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?>')">
                                                <i class="fas fa-user-cog"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    onclick="deleteUser(<?php echo $user->id; ?>, '<?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php else: ?>
                                            <span class="badge bg-info">Current User</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted" style="font-size: 64px;"></i>
                    <h4 class="mt-3 text-muted">No Users Found</h4>
                    <p class="text-muted">No users match your current filters.</p>
                    <a href="users.php" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>View All Users
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Action Forms -->
<form id="roleForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" value="toggle_role">
    <input type="hidden" name="user_id" id="roleUserId">
    <input type="hidden" name="new_role" id="newRole">
</form>

<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" value="delete_user">
    <input type="hidden" name="user_id" id="deleteUserId">
</form>

<script>
function toggleRole(userId, currentRole, userName) {
    const newRole = currentRole === 'admin' ? 'user' : 'admin';
    const action = newRole === 'admin' ? 'promote to administrator' : 'demote to regular user';

    if (confirm(`Are you sure you want to ${action} ${userName}?`)) {
        document.getElementById('roleUserId').value = userId;
        document.getElementById('newRole').value = newRole;
        document.getElementById('roleForm').submit();
    }
}

function deleteUser(userId, userName) {
    if (confirm(`Are you sure you want to delete user ${userName}? This action cannot be undone and will also delete all their bookings.`)) {
        document.getElementById('deleteUserId').value = userId;
        document.getElementById('deleteForm').submit();
    }
}
</script>

<?php include '../includes/footer.php'; ?>
