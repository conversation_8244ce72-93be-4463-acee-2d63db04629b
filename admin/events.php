<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Event Management';

// Require admin access
requireAdmin();

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'add':
                $eventData = [
                    'title' => sanitizeInput($_POST['title']),
                    'description' => sanitizeInput($_POST['description']),
                    'event_date' => $_POST['event_date'],
                    'event_time' => $_POST['event_time'],
                    'venue' => sanitizeInput($_POST['venue']),
                    'location' => sanitizeInput($_POST['location']),
                    'organizer' => sanitizeInput($_POST['organizer']),
                    'organizer_contact' => sanitizeInput($_POST['organizer_contact']),
                    'image_url' => sanitizeInput($_POST['image_url']),
                    'price' => floatval($_POST['price']),
                    'total_tickets' => intval($_POST['total_tickets']),
                    'category' => sanitizeInput($_POST['category'])
                ];

                if ($eventManager->addEvent($eventData)) {
                    $message = 'Event added successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to add event. Please try again.';
                    $messageType = 'danger';
                }
                break;

            case 'edit':
                $eventId = intval($_POST['event_id']);
                $eventData = [
                    'title' => sanitizeInput($_POST['title']),
                    'description' => sanitizeInput($_POST['description']),
                    'event_date' => $_POST['event_date'],
                    'event_time' => $_POST['event_time'],
                    'venue' => sanitizeInput($_POST['venue']),
                    'location' => sanitizeInput($_POST['location']),
                    'organizer' => sanitizeInput($_POST['organizer']),
                    'organizer_contact' => sanitizeInput($_POST['organizer_contact']),
                    'price' => floatval($_POST['price']),
                    'total_tickets' => intval($_POST['total_tickets']),
                    'category' => sanitizeInput($_POST['category'])
                ];

                if ($eventManager->updateEvent($eventId, $eventData)) {
                    $message = 'Event updated successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update event. Please try again.';
                    $messageType = 'danger';
                }
                break;

            case 'delete':
                $eventId = intval($_POST['event_id']);
                if ($eventManager->deleteEvent($eventId)) {
                    $message = 'Event cancelled successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to cancel event. Please try again.';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get all events for display
$db->query('SELECT * FROM events ORDER BY created_at DESC');
$events = $db->resultset();

// Get event for editing if specified
$editEvent = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editEvent = $eventManager->getEventById($_GET['edit']);
}

$showAddForm = isset($_GET['action']) && $_GET['action'] === 'add';
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-calendar-alt me-2"></i>Event Management</h1>
                    <p class="text-muted">Manage all events in the system</p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Event
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Add/Edit Event Form -->
    <?php if ($showAddForm || $editEvent): ?>
    <div class="card shadow mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-<?php echo $editEvent ? 'edit' : 'plus'; ?> me-2"></i>
                <?php echo $editEvent ? 'Edit Event' : 'Add New Event'; ?>
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" id="eventForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="<?php echo $editEvent ? 'edit' : 'add'; ?>">
                <?php if ($editEvent): ?>
                    <input type="hidden" name="event_id" value="<?php echo $editEvent->id; ?>">
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="title" class="form-label">Event Title *</label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="<?php echo $editEvent ? htmlspecialchars($editEvent->title) : ''; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="4" required><?php echo $editEvent ? htmlspecialchars($editEvent->description) : ''; ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="event_date" class="form-label">Event Date *</label>
                                    <input type="date" class="form-control" id="event_date" name="event_date"
                                           value="<?php echo $editEvent ? $editEvent->event_date : ''; ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="event_time" class="form-label">Event Time *</label>
                                    <input type="time" class="form-control" id="event_time" name="event_time"
                                           value="<?php echo $editEvent ? $editEvent->event_time : ''; ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="venue" class="form-label">Venue *</label>
                                    <input type="text" class="form-control" id="venue" name="venue"
                                           value="<?php echo $editEvent ? htmlspecialchars($editEvent->venue) : ''; ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Location *</label>
                                    <input type="text" class="form-control" id="location" name="location"
                                           value="<?php echo $editEvent ? htmlspecialchars($editEvent->location) : ''; ?>" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="category" class="form-label">Category *</label>
                            <select class="form-control" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Conference" <?php echo ($editEvent && $editEvent->category === 'Conference') ? 'selected' : ''; ?>>Conference</option>
                                <option value="Workshop" <?php echo ($editEvent && $editEvent->category === 'Workshop') ? 'selected' : ''; ?>>Workshop</option>
                                <option value="Concert" <?php echo ($editEvent && $editEvent->category === 'Concert') ? 'selected' : ''; ?>>Concert</option>
                                <option value="Festival" <?php echo ($editEvent && $editEvent->category === 'Festival') ? 'selected' : ''; ?>>Festival</option>
                                <option value="Sports" <?php echo ($editEvent && $editEvent->category === 'Sports') ? 'selected' : ''; ?>>Sports</option>
                                <option value="Exhibition" <?php echo ($editEvent && $editEvent->category === 'Exhibition') ? 'selected' : ''; ?>>Exhibition</option>
                                <option value="Other" <?php echo ($editEvent && $editEvent->category === 'Other') ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="price" class="form-label">Price (XAF) *</label>
                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0"
                                   value="<?php echo $editEvent ? $editEvent->price : ''; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="total_tickets" class="form-label">Total Tickets *</label>
                            <input type="number" class="form-control" id="total_tickets" name="total_tickets" min="1"
                                   value="<?php echo $editEvent ? $editEvent->total_tickets : ''; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="organizer" class="form-label">Organizer *</label>
                            <input type="text" class="form-control" id="organizer" name="organizer"
                                   value="<?php echo $editEvent ? htmlspecialchars($editEvent->organizer) : ''; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="organizer_contact" class="form-label">Organizer Contact</label>
                            <input type="text" class="form-control" id="organizer_contact" name="organizer_contact"
                                   value="<?php echo $editEvent ? htmlspecialchars($editEvent->organizer_contact) : ''; ?>">
                        </div>

                        <?php if (!$editEvent): ?>
                        <div class="mb-3">
                            <label for="image_url" class="form-label">Image URL</label>
                            <input type="url" class="form-control" id="image_url" name="image_url"
                                   placeholder="https://example.com/image.jpg">
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="events.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i><?php echo $editEvent ? 'Update Event' : 'Add Event'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>

    <!-- Events List -->
    <div class="card shadow">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>All Events</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($events)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Event</th>
                                <th>Date & Time</th>
                                <th>Location</th>
                                <th>Price</th>
                                <th>Tickets</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($events as $event): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'; ?>"
                                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;"
                                             alt="<?php echo htmlspecialchars($event->title); ?>">
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($event->title); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($event->category); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div><?php echo formatDate($event->event_date); ?></div>
                                        <small class="text-muted"><?php echo formatTime($event->event_time); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div><?php echo htmlspecialchars($event->venue); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($event->location); ?></small>
                                    </div>
                                </td>
                                <td><?php echo formatCurrency($event->price); ?></td>
                                <td>
                                    <div>
                                        <span class="badge bg-<?php echo $event->available_tickets > 0 ? 'success' : 'danger'; ?>">
                                            <?php echo $event->available_tickets; ?>/<?php echo $event->total_tickets; ?>
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $event->status === 'active' ? 'success' : ($event->status === 'cancelled' ? 'danger' : 'warning'); ?>">
                                        <?php echo ucfirst($event->status); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="../events/details.php?id=<?php echo $event->id; ?>"
                                           class="btn btn-outline-info btn-sm" target="_blank">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="?edit=<?php echo $event->id; ?>"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($event->status === 'active'): ?>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="confirmDelete(<?php echo $event->id; ?>, '<?php echo htmlspecialchars($event->title); ?>')">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times text-muted" style="font-size: 64px;"></i>
                    <h4 class="mt-3 text-muted">No Events Found</h4>
                    <p class="text-muted">Start by adding your first event!</p>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Event
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="event_id" id="deleteEventId">
</form>

<script>
function confirmDelete(eventId, eventTitle) {
    if (confirm(`Are you sure you want to cancel the event "${eventTitle}"? This action cannot be undone.`)) {
        document.getElementById('deleteEventId').value = eventId;
        document.getElementById('deleteForm').submit();
    }
}

// Setup real-time validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('eventForm');
    if (form) {
        setupRealTimeValidation('eventForm');
    }
});
</script>

<?php include '../includes/footer.php'; ?>