<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Reports & Analytics';

// Require admin access
requireAdmin();

// Get date range from parameters
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-d'); // Today

// Revenue Analytics
$db->query('SELECT
    DATE(created_at) as date,
    COUNT(*) as bookings_count,
    SUM(total_amount) as daily_revenue
    FROM bookings
    WHERE booking_status = "confirmed"
    AND DATE(created_at) BETWEEN :start_date AND :end_date
    GROUP BY DATE(created_at)
    ORDER BY date DESC');
$db->bind(':start_date', $startDate);
$db->bind(':end_date', $endDate);
$dailyRevenue = $db->resultset();

// Event Performance
$db->query('SELECT
    e.title,
    e.total_tickets,
    e.available_tickets,
    (e.total_tickets - e.available_tickets) as tickets_sold,
    ROUND(((e.total_tickets - e.available_tickets) / e.total_tickets) * 100, 2) as occupancy_rate,
    COUNT(b.id) as total_bookings,
    SUM(CASE WHEN b.booking_status = "confirmed" THEN b.total_amount ELSE 0 END) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id
    WHERE e.status = "active"
    GROUP BY e.id
    ORDER BY revenue DESC');
$eventPerformance = $db->resultset();

// Customer Analytics
$db->query('SELECT
    u.first_name,
    u.last_name,
    u.email,
    COUNT(b.id) as total_bookings,
    SUM(b.total_amount) as total_spent,
    MAX(b.created_at) as last_booking
    FROM users u
    JOIN bookings b ON u.id = b.user_id
    WHERE b.booking_status = "confirmed"
    GROUP BY u.id
    ORDER BY total_spent DESC
    LIMIT 10');
$topCustomers = $db->resultset();

// Category Performance
$db->query('SELECT
    e.category,
    COUNT(DISTINCT e.id) as events_count,
    COUNT(b.id) as total_bookings,
    SUM(CASE WHEN b.booking_status = "confirmed" THEN b.total_amount ELSE 0 END) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id
    WHERE e.status = "active"
    GROUP BY e.category
    ORDER BY revenue DESC');
$categoryPerformance = $db->resultset();

// Overall Statistics
$db->query('SELECT
    COUNT(DISTINCT e.id) as total_events,
    COUNT(DISTINCT u.id) as total_customers,
    COUNT(b.id) as total_bookings,
    SUM(CASE WHEN b.booking_status = "confirmed" THEN b.total_amount ELSE 0 END) as total_revenue,
    AVG(CASE WHEN b.booking_status = "confirmed" THEN b.total_amount ELSE NULL END) as avg_booking_value
    FROM events e
    CROSS JOIN users u
    LEFT JOIN bookings b ON 1=1
    WHERE e.status = "active" AND u.role = "user"');
$overallStats = $db->single();

// Monthly Trends
$db->query('SELECT
    DATE_FORMAT(created_at, "%Y-%m") as month,
    COUNT(*) as bookings,
    SUM(total_amount) as revenue
    FROM bookings
    WHERE booking_status = "confirmed"
    AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, "%Y-%m")
    ORDER BY month DESC');
$monthlyTrends = $db->resultset();
?>

<?php include '../includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-chart-bar me-2"></i>Reports & Analytics</h1>
                    <p class="text-muted">Comprehensive business insights and performance metrics</p>
                </div>
                <div>
                    <a href="index.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="fas fa-print me-2"></i>Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date"
                           value="<?php echo $startDate; ?>">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date"
                           value="<?php echo $endDate; ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i>Apply Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $overallStats->total_events ?? 0; ?></h3>
                            <p class="mb-0">Total Events</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $overallStats->total_customers ?? 0; ?></h3>
                            <p class="mb-0">Total Customers</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo $overallStats->total_bookings ?? 0; ?></h3>
                            <p class="mb-0">Total Bookings</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ticket-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><?php echo formatCurrency($overallStats->total_revenue ?? 0); ?></h3>
                            <p class="mb-0">Total Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Event Performance -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Event Performance</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($eventPerformance)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Tickets Sold</th>
                                        <th>Occupancy</th>
                                        <th>Bookings</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($eventPerformance as $event): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($event->title); ?></td>
                                        <td><?php echo $event->tickets_sold; ?>/<?php echo $event->total_tickets; ?></td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar"
                                                     style="width: <?php echo $event->occupancy_rate; ?>%">
                                                    <?php echo $event->occupancy_rate; ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo $event->total_bookings; ?></td>
                                        <td><?php echo formatCurrency($event->revenue); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line text-muted" style="font-size: 48px;"></i>
                            <h5 class="mt-3 text-muted">No Event Data</h5>
                            <p class="text-muted">Event performance data will appear here once you have events with bookings.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>Top Customers</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($topCustomers)): ?>
                        <?php foreach ($topCustomers as $index => $customer): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="badge bg-primary rounded-circle me-3" style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;">
                                    <?php echo $index + 1; ?>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($customer->first_name . ' ' . $customer->last_name); ?></h6>
                                    <small class="text-muted"><?php echo $customer->total_bookings; ?> bookings</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold"><?php echo formatCurrency($customer->total_spent); ?></div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users text-muted" style="font-size: 48px;"></i>
                            <h6 class="mt-3 text-muted">No Customer Data</h6>
                            <p class="text-muted small">Customer analytics will appear here once you have confirmed bookings.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Performance & Daily Revenue -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Category Performance</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($categoryPerformance)): ?>
                        <?php foreach ($categoryPerformance as $category): ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><?php echo htmlspecialchars($category->category); ?></span>
                                <span class="fw-bold"><?php echo formatCurrency($category->revenue); ?></span>
                            </div>
                            <div class="d-flex justify-content-between text-muted small">
                                <span><?php echo $category->events_count; ?> events</span>
                                <span><?php echo $category->total_bookings; ?> bookings</span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-tags text-muted" style="font-size: 48px;"></i>
                            <h6 class="mt-3 text-muted">No Category Data</h6>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Daily Revenue (<?php echo $startDate; ?> to <?php echo $endDate; ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($dailyRevenue)): ?>
                        <div style="max-height: 300px; overflow-y: auto;">
                            <?php foreach ($dailyRevenue as $day): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <div><?php echo date('M j, Y', strtotime($day->date)); ?></div>
                                    <small class="text-muted"><?php echo $day->bookings_count; ?> bookings</small>
                                </div>
                                <div class="fw-bold"><?php echo formatCurrency($day->daily_revenue); ?></div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line text-muted" style="font-size: 48px;"></i>
                            <h6 class="mt-3 text-muted">No Revenue Data</h6>
                            <p class="text-muted small">Daily revenue data will appear here for the selected date range.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Trends -->
    <div class="card shadow mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Monthly Trends (Last 12 Months)</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($monthlyTrends)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Month</th>
                                <th>Bookings</th>
                                <th>Revenue</th>
                                <th>Avg. Booking Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($monthlyTrends as $month): ?>
                            <tr>
                                <td><?php echo date('F Y', strtotime($month->month . '-01')); ?></td>
                                <td><?php echo $month->bookings; ?></td>
                                <td><?php echo formatCurrency($month->revenue); ?></td>
                                <td><?php echo formatCurrency($month->bookings > 0 ? $month->revenue / $month->bookings : 0); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-chart-area text-muted" style="font-size: 48px;"></i>
                    <h5 class="mt-3 text-muted">No Monthly Data</h5>
                    <p class="text-muted">Monthly trend data will appear here once you have booking history.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Export Options -->
    <div class="card shadow">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-download me-2"></i>Export Options</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <button class="btn btn-outline-success w-100" onclick="exportData('csv')">
                        <i class="fas fa-file-csv me-2"></i>Export to CSV
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-danger w-100" onclick="exportData('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>Export to PDF
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-primary w-100" onclick="exportData('excel')">
                        <i class="fas fa-file-excel me-2"></i>Export to Excel
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-info w-100" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportData(format) {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    // This would typically make an AJAX call to a server-side export script
    alert(`Export to ${format.toUpperCase()} functionality would be implemented here.\nDate range: ${startDate} to ${endDate}`);

    // Example implementation:
    // window.location.href = `export.php?format=${format}&start_date=${startDate}&end_date=${endDate}`;
}

// Print styles
const printStyles = `
    @media print {
        .btn, .card-header .btn, .no-print { display: none !important; }
        .card { border: 1px solid #ddd !important; box-shadow: none !important; }
        .container { max-width: 100% !important; }
    }
`;

// Add print styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = printStyles;
document.head.appendChild(styleSheet);
</script>

<?php include '../includes/footer.php'; ?>