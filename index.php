<?php
// Backend-only Event Booking System
// No frontend UI - API endpoints only

require_once 'includes/config.php';
require_once 'includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = trim($path, '/');

// Simple API router
switch ($path) {
    case '':
    case 'api':
        // API documentation endpoint
        echo json_encode([
            'message' => 'Event Booking System - Backend API Only',
            'version' => '1.0.0',
            'endpoints' => [
                'GET /api/events' => 'Get all events',
                'GET /api/events/{id}' => 'Get specific event',
                'POST /api/events' => 'Create new event (admin only)',
                'PUT /api/events/{id}' => 'Update event (admin only)',
                'DELETE /api/events/{id}' => 'Delete event (admin only)',
                'GET /api/bookings' => 'Get all bookings (admin only)',
                'POST /api/bookings' => 'Create new booking',
                'GET /api/users' => 'Get all users (admin only)',
                'POST /api/auth/login' => 'User authentication',
                'POST /api/auth/register' => 'User registration',
                'GET /api/stats' => 'System statistics (admin only)'
            ],
            'database_status' => testDatabaseConnection() ? 'connected' : 'disconnected'
        ]);
        break;

    case 'api/events':
        handleEventsAPI($method);
        break;

    case 'api/bookings':
        handleBookingsAPI($method);
        break;

    case 'api/users':
        handleUsersAPI($method);
        break;

    case 'api/auth/login':
        handleLoginAPI($method);
        break;

    case 'api/auth/register':
        handleRegisterAPI($method);
        break;

    case 'api/stats':
        handleStatsAPI($method);
        break;

    default:
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
        break;
}

// API Handler Functions

function handleEventsAPI($method) {
    global $eventManager;

    switch ($method) {
        case 'GET':
            $events = $eventManager->getAllEvents();
            echo json_encode(['success' => true, 'data' => $events]);
            break;

        case 'POST':
            // Require admin authentication
            if (!isAdmin()) {
                http_response_code(401);
                echo json_encode(['error' => 'Admin access required']);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);
            if ($eventManager->addEvent($input)) {
                echo json_encode(['success' => true, 'message' => 'Event created']);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create event']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

function handleBookingsAPI($method) {
    global $bookingManager, $db;

    switch ($method) {
        case 'GET':
            if (!isAdmin()) {
                http_response_code(401);
                echo json_encode(['error' => 'Admin access required']);
                return;
            }

            $db->query('SELECT * FROM bookings ORDER BY created_at DESC');
            $bookings = $db->resultset();
            echo json_encode(['success' => true, 'data' => $bookings]);
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            $result = $bookingManager->createBooking($input);
            if ($result) {
                echo json_encode(['success' => true, 'booking_reference' => $result]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create booking']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

function handleUsersAPI($method) {
    global $db;

    if (!isAdmin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Admin access required']);
        return;
    }

    switch ($method) {
        case 'GET':
            $db->query('SELECT id, username, first_name, last_name, email, role, created_at FROM users ORDER BY created_at DESC');
            $users = $db->resultset();
            echo json_encode(['success' => true, 'data' => $users]);
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

function handleLoginAPI($method) {
    global $userManager;

    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';

    if ($userManager->login($username, $password)) {
        echo json_encode(['success' => true, 'message' => 'Login successful']);
    } else {
        http_response_code(401);
        echo json_encode(['error' => 'Invalid credentials']);
    }
}

function handleRegisterAPI($method) {
    global $userManager;

    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if ($userManager->register($input)) {
        echo json_encode(['success' => true, 'message' => 'Registration successful']);
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Registration failed']);
    }
}

function handleStatsAPI($method) {
    global $db;

    if (!isAdmin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Admin access required']);
        return;
    }

    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    // Get system statistics
    $db->query('SELECT
        (SELECT COUNT(*) FROM events WHERE status = "active") as total_events,
        (SELECT COUNT(*) FROM users WHERE role = "user") as total_users,
        (SELECT COUNT(*) FROM bookings) as total_bookings,
        (SELECT SUM(total_amount) FROM bookings WHERE booking_status = "confirmed") as total_revenue
    ');
    $stats = $db->single();

    echo json_encode(['success' => true, 'data' => $stats]);
}

function testDatabaseConnection() {
    global $db;
    try {
        $db->query('SELECT 1');
        return true;
    } catch (Exception $e) {
        return false;
    }
}


?>
