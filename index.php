<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$pageTitle = 'Home';

// Get featured events (latest 6 events)
$featuredEvents = $eventManager->getAllEvents(6);
?>

<?php include 'includes/header.php'; ?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Discover Amazing Events</h1>
                <p class="lead mb-4">Find and book tickets for concerts, conferences, workshops, and more. Your next unforgettable experience is just a click away.</p>
                <a href="events/search.php" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-search me-2"></i>Browse Events
                </a>
                <?php if (!isLoggedIn()): ?>
                <a href="auth/register.php" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-user-plus me-2"></i>Join Now
                </a>
                <?php endif; ?>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-calendar-check" style="font-size: 200px; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-body p-4">
                        <h3 class="text-center mb-4">Find Your Perfect Event</h3>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="searchTerm" placeholder="Event name or keyword">
                            </div>
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchLocation" placeholder="Location">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="searchDate">
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="performSearch()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Events Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5">Featured Events</h2>
            </div>
        </div>

        <?php if (!empty($featuredEvents)): ?>
        <div class="row">
            <?php foreach ($featuredEvents as $event): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card event-card h-100">
                    <div class="position-relative">
                        <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'; ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($event->title); ?>"
                             style="height: 200px; object-fit: cover;"
                             loading="lazy">
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-primary"><?php echo formatCurrency($event->price); ?></span>
                        </div>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?php echo htmlspecialchars($event->title); ?></h5>
                        <p class="card-text text-muted small mb-2">
                            <i class="fas fa-calendar me-1"></i><?php echo formatDate($event->event_date); ?> at <?php echo formatTime($event->event_time); ?>
                        </p>
                        <p class="card-text text-muted small mb-2">
                            <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($event->venue . ', ' . $event->location); ?>
                        </p>
                        <p class="card-text flex-grow-1"><?php echo substr(htmlspecialchars($event->description), 0, 100) . '...'; ?></p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-ticket-alt me-1"></i><?php echo $event->available_tickets; ?> tickets left
                                </small>
                                <div>
                                    <a href="events/details.php?id=<?php echo $event->id; ?>" class="btn btn-outline-primary btn-sm me-2">
                                        <i class="fas fa-info-circle"></i> Details
                                    </a>
                                    <?php if ($event->available_tickets > 0): ?>
                                    <button class="btn btn-primary btn-sm" onclick="addToCart(<?php echo $event->id; ?>)">
                                        <i class="fas fa-cart-plus"></i> Add to Cart
                                    </button>
                                    <?php else: ?>
                                    <button class="btn btn-secondary btn-sm" disabled>
                                        <i class="fas fa-times"></i> Sold Out
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-4">
            <a href="events/search.php" class="btn btn-primary btn-lg">
                <i class="fas fa-eye me-2"></i>View All Events
            </a>
        </div>
        <?php else: ?>
        <div class="text-center">
            <div class="py-5">
                <i class="fas fa-calendar-times text-muted" style="font-size: 64px;"></i>
                <h3 class="mt-3 text-muted">No Events Available</h3>
                <p class="text-muted">Check back later for exciting events!</p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5">Why Choose EventBooking?</h2>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 text-center mb-4">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <i class="fas fa-search text-primary mb-3" style="font-size: 48px;"></i>
                        <h4>Easy Discovery</h4>
                        <p>Find events that match your interests with our powerful search and filtering tools.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <i class="fas fa-shield-alt text-primary mb-3" style="font-size: 48px;"></i>
                        <h4>Secure Booking</h4>
                        <p>Your personal information and payments are protected with industry-standard security.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <i class="fas fa-mobile-alt text-primary mb-3" style="font-size: 48px;"></i>
                        <h4>Mobile Friendly</h4>
                        <p>Book events on the go with our responsive design that works on all devices.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
