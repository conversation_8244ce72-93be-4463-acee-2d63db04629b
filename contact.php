<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/email.php';

$pageTitle = 'Contact Us';
$message = '';
$messageType = '';

// Process contact form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token.';
        $messageType = 'danger';
    } else {
        $name = sanitizeInput($_POST['name']);
        $email = sanitizeInput($_POST['email']);
        $subject = sanitizeInput($_POST['subject']);
        $messageText = sanitizeInput($_POST['message']);

        // Validation
        if (empty($name) || empty($email) || empty($subject) || empty($messageText)) {
            $message = 'Please fill in all required fields.';
            $messageType = 'danger';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address.';
            $messageType = 'danger';
        } else {
            // Send email
            try {
                $emailSent = $emailManager->sendContactMessage($name, $email, $subject, $messageText);

                if ($emailSent) {
                    $message = 'Thank you for your message! We will get back to you soon.';
                    $messageType = 'success';
                    // Clear form data
                    $_POST = [];
                } else {
                    $message = 'Sorry, there was an error sending your message. Please try again later.';
                    $messageType = 'danger';
                }
            } catch (Exception $e) {
                $message = 'Sorry, there was an error sending your message. Please try again later.';
                $messageType = 'danger';
                error_log("Contact form error: " . $e->getMessage());
            }
        }
    }
}
?>

<?php include 'includes/header.php'; ?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item active">Contact Us</li>
                </ol>
            </nav>
            <h1><i class="fas fa-envelope me-2"></i>Contact Us</h1>
            <p class="text-muted">Get in touch with us for any questions or support</p>
        </div>
    </div>

    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-paper-plane me-2"></i>Send us a Message</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="contactForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Your Name *</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>"
                                       required minlength="2" maxlength="100">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Your Email *</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                                       required maxlength="255">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject *</label>
                            <select class="form-select" id="subject" name="subject" required>
                                <option value="">Select a subject</option>
                                <option value="General Inquiry" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'General Inquiry') ? 'selected' : ''; ?>>General Inquiry</option>
                                <option value="Booking Support" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Booking Support') ? 'selected' : ''; ?>>Booking Support</option>
                                <option value="Event Information" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Event Information') ? 'selected' : ''; ?>>Event Information</option>
                                <option value="Technical Issue" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Technical Issue') ? 'selected' : ''; ?>>Technical Issue</option>
                                <option value="Refund Request" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Refund Request') ? 'selected' : ''; ?>>Refund Request</option>
                                <option value="Partnership" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Partnership') ? 'selected' : ''; ?>>Partnership Opportunity</option>
                                <option value="Other" <?php echo (isset($_POST['subject']) && $_POST['subject'] === 'Other') ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Your Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="6"
                                      placeholder="Please describe your inquiry in detail..."
                                      required minlength="10" maxlength="2000"><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                            <div class="form-text">Minimum 10 characters, maximum 2000 characters</div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Contact Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6><i class="fas fa-envelope text-primary me-2"></i>Email</h6>
                        <p class="mb-0"><?php echo ADMIN_EMAIL; ?></p>
                    </div>

                    <div class="mb-4">
                        <h6><i class="fas fa-phone text-primary me-2"></i>Phone</h6>
                        <p class="mb-0">
                            <a href="tel:+237651408682" class="text-decoration-none">+237 651 408 682</a>
                        </p>
                        <small class="text-muted">Click to call directly</small>
                    </div>

                    <div class="mb-4">
                        <h6><i class="fab fa-whatsapp text-success me-2"></i>WhatsApp</h6>
                        <p class="mb-0">
                            <a href="https://wa.me/237651408682?text=Hello%20Fritz,%20I%20have%20a%20question%20about%20event%20booking"
                               target="_blank" class="text-decoration-none text-success">+237 651 408 682</a>
                        </p>
                        <small class="text-muted">Message me on WhatsApp</small>
                    </div>

                    <div class="mb-4">
                        <h6><i class="fab fa-telegram text-info me-2"></i>Telegram</h6>
                        <p class="mb-0">
                            <a href="https://t.me/+237651408682" target="_blank" class="text-decoration-none text-info">@fritztayong</a>
                        </p>
                        <small class="text-muted">Chat with me on Telegram</small>
                    </div>

                    <div class="mb-4">
                        <h6><i class="fas fa-clock text-primary me-2"></i>Business Hours</h6>
                        <p class="mb-1">Monday - Friday: 9:00 AM - 6:00 PM</p>
                        <p class="mb-1">Saturday: 10:00 AM - 4:00 PM</p>
                        <p class="mb-0">Sunday: Closed</p>
                    </div>

                    <div class="mb-4">
                        <h6><i class="fas fa-map-marker-alt text-primary me-2"></i>Service Areas</h6>
                        <p class="mb-0">Central African Region</p>
                        <small class="text-muted">Cameroon, Gabon, CAR, Chad, Equatorial Guinea, Republic of the Congo, DRC</small>
                    </div>

                    <div class="mt-4">
                        <h6><i class="fas fa-rocket text-primary me-2"></i>Quick Actions</h6>
                        <div class="d-grid gap-2">
                            <a href="mailto:<EMAIL>?subject=Event Booking Inquiry&body=Hello Fritz,%0D%0A%0D%0AI am interested in your event booking services.%0D%0A%0D%0APlease contact me at your earliest convenience.%0D%0A%0D%0AThank you!"
                               class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>Send Quick Email
                            </a>
                            <a href="https://wa.me/237651408682?text=Hello%20Fritz,%20I%20need%20help%20with%20event%20booking"
                               target="_blank" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>WhatsApp Now
                            </a>
                            <a href="tel:+237651408682" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>Call Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>Frequently Asked Questions</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    How do I cancel my booking?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Contact us with your booking reference number. Cancellation policies vary by event.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Can I transfer my ticket?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Most tickets can be transferred to another person. Please contact us for assistance.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    What payment methods do you accept?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    We accept credit cards, PayPal, and bank transfers. All payments are processed securely.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Setup real-time validation if function exists
    if (typeof setupRealTimeValidation === 'function') {
        setupRealTimeValidation('contactForm');
    }

    // Character counter for message field
    const messageField = document.getElementById('message');
    if (messageField) {
        const maxLength = 2000;
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.style.fontSize = '0.875rem';
        messageField.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - messageField.value.length;
            counter.textContent = `${messageField.value.length}/${maxLength} characters`;
            counter.className = remaining < 100 ? 'form-text text-end text-warning' : 'form-text text-end text-muted';
        }

        messageField.addEventListener('input', updateCounter);
        updateCounter();
    }
});
</script>

<?php include 'includes/footer.php'; ?>
