<?php
require_once 'config.php';

// User management functions
class UserManager {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    public function register($username, $email, $password, $firstName, $lastName, $phone = '', $address = '') {
        // Check if user already exists
        $this->db->query('SELECT id FROM users WHERE username = :username OR email = :email');
        $this->db->bind(':username', $username);
        $this->db->bind(':email', $email);

        if ($this->db->single()) {
            return false; // User already exists
        }

        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Insert new user
        $this->db->query('INSERT INTO users (username, email, password, first_name, last_name, phone, address)
                         VALUES (:username, :email, :password, :first_name, :last_name, :phone, :address)');
        $this->db->bind(':username', $username);
        $this->db->bind(':email', $email);
        $this->db->bind(':password', $hashedPassword);
        $this->db->bind(':first_name', $firstName);
        $this->db->bind(':last_name', $lastName);
        $this->db->bind(':phone', $phone);
        $this->db->bind(':address', $address);

        return $this->db->execute();
    }

    public function login($username, $password) {
        $this->db->query('SELECT * FROM users WHERE username = :username OR email = :username');
        $this->db->bind(':username', $username);
        $user = $this->db->single();

        if ($user && password_verify($password, $user->password)) {
            $_SESSION['user_id'] = $user->id;
            $_SESSION['username'] = $user->username;
            $_SESSION['user_role'] = $user->role;
            $_SESSION['user_name'] = $user->first_name . ' ' . $user->last_name;
            return true;
        }
        return false;
    }

    public function logout() {
        session_destroy();
        return true;
    }

    public function getUserById($id) {
        $this->db->query('SELECT * FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    public function updateProfile($userId, $data) {
        $this->db->query('UPDATE users SET first_name = :first_name, last_name = :last_name,
                         phone = :phone, address = :address WHERE id = :id');
        $this->db->bind(':first_name', $data['first_name']);
        $this->db->bind(':last_name', $data['last_name']);
        $this->db->bind(':phone', $data['phone']);
        $this->db->bind(':address', $data['address']);
        $this->db->bind(':id', $userId);
        return $this->db->execute();
    }
}

// Event management functions
class EventManager {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    public function getAllEvents($limit = null, $offset = 0) {
        $query = 'SELECT * FROM events WHERE status = "active" ORDER BY event_date ASC';
        if ($limit) {
            $query .= ' LIMIT :limit OFFSET :offset';
        }

        $this->db->query($query);
        if ($limit) {
            $this->db->bind(':limit', $limit);
            $this->db->bind(':offset', $offset);
        }
        return $this->db->resultset();
    }

    public function getEventById($id) {
        $this->db->query('SELECT * FROM events WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    public function searchEvents($searchTerm, $location = '', $date = '') {
        $query = 'SELECT * FROM events WHERE status = "active" AND (title LIKE :search OR description LIKE :search)';
        $params = [':search' => "%$searchTerm%"];

        if (!empty($location)) {
            $query .= ' AND location LIKE :location';
            $params[':location'] = "%$location%";
        }

        if (!empty($date)) {
            $query .= ' AND event_date = :date';
            $params[':date'] = $date;
        }

        $query .= ' ORDER BY event_date ASC';

        $this->db->query($query);
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        return $this->db->resultset();
    }

    public function getEventsByCategory($category) {
        $this->db->query('SELECT * FROM events WHERE category = :category AND status = "active" ORDER BY event_date ASC');
        $this->db->bind(':category', $category);
        return $this->db->resultset();
    }

    public function addEvent($data) {
        $this->db->query('INSERT INTO events (title, description, event_date, event_time, venue, location,
                         organizer, organizer_contact, image_url, price, total_tickets, available_tickets, category)
                         VALUES (:title, :description, :event_date, :event_time, :venue, :location,
                         :organizer, :organizer_contact, :image_url, :price, :total_tickets, :available_tickets, :category)');

        $this->db->bind(':title', $data['title']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':event_date', $data['event_date']);
        $this->db->bind(':event_time', $data['event_time']);
        $this->db->bind(':venue', $data['venue']);
        $this->db->bind(':location', $data['location']);
        $this->db->bind(':organizer', $data['organizer']);
        $this->db->bind(':organizer_contact', $data['organizer_contact']);
        $this->db->bind(':image_url', $data['image_url']);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':total_tickets', $data['total_tickets']);
        $this->db->bind(':available_tickets', $data['total_tickets']);
        $this->db->bind(':category', $data['category']);

        return $this->db->execute();
    }

    public function updateEvent($id, $data) {
        $this->db->query('UPDATE events SET title = :title, description = :description, event_date = :event_date,
                         event_time = :event_time, venue = :venue, location = :location, organizer = :organizer,
                         organizer_contact = :organizer_contact, price = :price, total_tickets = :total_tickets,
                         category = :category WHERE id = :id');

        $this->db->bind(':title', $data['title']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':event_date', $data['event_date']);
        $this->db->bind(':event_time', $data['event_time']);
        $this->db->bind(':venue', $data['venue']);
        $this->db->bind(':location', $data['location']);
        $this->db->bind(':organizer', $data['organizer']);
        $this->db->bind(':organizer_contact', $data['organizer_contact']);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':total_tickets', $data['total_tickets']);
        $this->db->bind(':category', $data['category']);
        $this->db->bind(':id', $id);

        return $this->db->execute();
    }

    public function deleteEvent($id) {
        $this->db->query('UPDATE events SET status = "cancelled" WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    public function updateAvailableTickets($eventId, $quantity) {
        $this->db->query('UPDATE events SET available_tickets = available_tickets - :quantity WHERE id = :id');
        $this->db->bind(':quantity', $quantity);
        $this->db->bind(':id', $eventId);
        return $this->db->execute();
    }
}

// Cart management functions
class CartManager {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    public function addToCart($userId, $eventId, $quantity = 1) {
        // Check if event exists and has available tickets
        $event = $this->getEventById($eventId);
        if (!$event || $event->available_tickets < $quantity) {
            return false;
        }

        // Check if item already exists in cart
        $this->db->query('SELECT * FROM cart WHERE user_id = :user_id AND event_id = :event_id');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $existingItem = $this->db->single();

        if ($existingItem) {
            // Update quantity
            $newQuantity = $existingItem->quantity + $quantity;
            if ($newQuantity > $event->available_tickets) {
                return false;
            }

            $this->db->query('UPDATE cart SET quantity = :quantity WHERE user_id = :user_id AND event_id = :event_id');
            $this->db->bind(':quantity', $newQuantity);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':event_id', $eventId);
        } else {
            // Add new item
            $this->db->query('INSERT INTO cart (user_id, event_id, quantity) VALUES (:user_id, :event_id, :quantity)');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':quantity', $quantity);
        }

        return $this->db->execute();
    }

    public function getCartItems($userId) {
        $this->db->query('SELECT c.*, e.title, e.price, e.event_date, e.event_time, e.venue, e.available_tickets, e.image_url
                         FROM cart c
                         JOIN events e ON c.event_id = e.id
                         WHERE c.user_id = :user_id AND e.status = "active"
                         ORDER BY c.added_at DESC');
        $this->db->bind(':user_id', $userId);
        return $this->db->resultset();
    }

    public function getCartCount($userId) {
        $this->db->query('SELECT SUM(quantity) as total FROM cart WHERE user_id = :user_id');
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();
        return $result ? (int)$result->total : 0;
    }

    public function updateCartItem($userId, $eventId, $quantity) {
        if ($quantity <= 0) {
            return $this->removeFromCart($userId, $eventId);
        }

        $this->db->query('UPDATE cart SET quantity = :quantity WHERE user_id = :user_id AND event_id = :event_id');
        $this->db->bind(':quantity', $quantity);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        return $this->db->execute();
    }

    public function removeFromCart($userId, $eventId) {
        $this->db->query('DELETE FROM cart WHERE user_id = :user_id AND event_id = :event_id');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        return $this->db->execute();
    }

    public function clearCart($userId) {
        $this->db->query('DELETE FROM cart WHERE user_id = :user_id');
        $this->db->bind(':user_id', $userId);
        return $this->db->execute();
    }

    public function getCartTotal($userId) {
        $this->db->query('SELECT SUM(c.quantity * e.price) as total
                         FROM cart c
                         JOIN events e ON c.event_id = e.id
                         WHERE c.user_id = :user_id AND e.status = "active"');
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();
        return $result ? (float)$result->total : 0;
    }

    private function getEventById($eventId) {
        $this->db->query('SELECT * FROM events WHERE id = :id');
        $this->db->bind(':id', $eventId);
        return $this->db->single();
    }
}

// Booking management functions
class BookingManager {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    public function createBooking($userId, $eventId, $quantity, $attendeeData) {
        // Start transaction
        $this->db->getConnection()->beginTransaction();

        try {
            // Check event availability
            $this->db->query('SELECT * FROM events WHERE id = :id AND available_tickets >= :quantity');
            $this->db->bind(':id', $eventId);
            $this->db->bind(':quantity', $quantity);
            $event = $this->db->single();

            if (!$event) {
                throw new Exception('Event not available or insufficient tickets');
            }

            // Calculate total amount
            $totalAmount = $quantity * $event->price;

            // Generate booking reference
            $bookingReference = generateBookingReference();

            // Create booking
            $this->db->query('INSERT INTO bookings (user_id, event_id, quantity, total_amount, booking_reference,
                             attendee_name, attendee_email, attendee_phone, special_requirements)
                             VALUES (:user_id, :event_id, :quantity, :total_amount, :booking_reference,
                             :attendee_name, :attendee_email, :attendee_phone, :special_requirements)');

            $this->db->bind(':user_id', $userId);
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':quantity', $quantity);
            $this->db->bind(':total_amount', $totalAmount);
            $this->db->bind(':booking_reference', $bookingReference);
            $this->db->bind(':attendee_name', $attendeeData['name']);
            $this->db->bind(':attendee_email', $attendeeData['email']);
            $this->db->bind(':attendee_phone', $attendeeData['phone']);
            $this->db->bind(':special_requirements', $attendeeData['special_requirements']);

            if (!$this->db->execute()) {
                throw new Exception('Failed to create booking');
            }

            $bookingId = $this->db->lastInsertId();

            // Update available tickets
            $this->db->query('UPDATE events SET available_tickets = available_tickets - :quantity WHERE id = :id');
            $this->db->bind(':quantity', $quantity);
            $this->db->bind(':id', $eventId);

            if (!$this->db->execute()) {
                throw new Exception('Failed to update ticket availability');
            }

            // Commit transaction
            $this->db->getConnection()->commit();

            return $bookingId;

        } catch (Exception $e) {
            // Rollback transaction
            $this->db->getConnection()->rollback();
            return false;
        }
    }

    public function getUserBookings($userId) {
        $this->db->query('SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.image_url
                         FROM bookings b
                         JOIN events e ON b.event_id = e.id
                         WHERE b.user_id = :user_id
                         ORDER BY b.created_at DESC');
        $this->db->bind(':user_id', $userId);
        return $this->db->resultset();
    }

    public function getBookingByReference($reference) {
        $this->db->query('SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.organizer, e.organizer_contact
                         FROM bookings b
                         JOIN events e ON b.event_id = e.id
                         WHERE b.booking_reference = :reference');
        $this->db->bind(':reference', $reference);
        return $this->db->single();
    }

    public function confirmBooking($bookingId) {
        $this->db->query('UPDATE bookings SET booking_status = "confirmed", payment_status = "completed" WHERE id = :id');
        $this->db->bind(':id', $bookingId);
        return $this->db->execute();
    }

    public function cancelBooking($bookingId) {
        // Start transaction
        $this->db->getConnection()->beginTransaction();

        try {
            // Get booking details
            $this->db->query('SELECT * FROM bookings WHERE id = :id');
            $this->db->bind(':id', $bookingId);
            $booking = $this->db->single();

            if (!$booking) {
                throw new Exception('Booking not found');
            }

            // Update booking status
            $this->db->query('UPDATE bookings SET booking_status = "cancelled" WHERE id = :id');
            $this->db->bind(':id', $bookingId);

            if (!$this->db->execute()) {
                throw new Exception('Failed to cancel booking');
            }

            // Restore available tickets
            $this->db->query('UPDATE events SET available_tickets = available_tickets + :quantity WHERE id = :id');
            $this->db->bind(':quantity', $booking->quantity);
            $this->db->bind(':id', $booking->event_id);

            if (!$this->db->execute()) {
                throw new Exception('Failed to restore ticket availability');
            }

            // Commit transaction
            $this->db->getConnection()->commit();
            return true;

        } catch (Exception $e) {
            // Rollback transaction
            $this->db->getConnection()->rollback();
            return false;
        }
    }
}

// Initialize managers
$userManager = new UserManager($db);
$eventManager = new EventManager($db);
$cartManager = new CartManager($db);
$bookingManager = new BookingManager($db);
?>
