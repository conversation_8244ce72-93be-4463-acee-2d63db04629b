    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-calendar-alt me-2"></i>EventBooking</h5>
                    <p>Your premier destination for discovering and booking amazing events. From concerts to conferences, we've got you covered.</p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo SITE_URL; ?>/index.php" class="text-light text-decoration-none">Home</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/events/search.php" class="text-light text-decoration-none">Browse Events</a></li>
                        <?php if (isLoggedIn()): ?>
                            <li><a href="<?php echo SITE_URL; ?>/user/dashboard.php" class="text-light text-decoration-none">My Dashboard</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-envelope me-2"></i><?php echo ADMIN_EMAIL; ?></p>
                    <p><i class="fas fa-phone me-2"></i>+237651408682</p>
                    <div class="mt-3">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> EventBooking System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-light text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-light text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="/assets/js/main.js"></script>
    <script src="/assets/js/contact-button.js"></script>

    <script>
        // Update cart count on page load
        $(document).ready(function() {
            updateCartCount();
        });

        function updateCartCount() {
            <?php if (isLoggedIn()): ?>
            $.get('/booking/get_cart_count.php', function(data) {
                $('#cartCount').text(data.count);
                if (data.count > 0) {
                    $('#cartCount').show();
                } else {
                    $('#cartCount').hide();
                }
            });
            <?php endif; ?>
        }

        // Add to cart function
        function addToCart(eventId, quantity = 1) {
            <?php if (isLoggedIn()): ?>
            $.post('/booking/add_to_cart.php', {
                event_id: eventId,
                quantity: quantity,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            }, function(response) {
                if (response.success) {
                    updateCartCount();
                    showAlert('success', 'Event added to cart successfully!');
                } else {
                    showAlert('danger', response.message || 'Failed to add event to cart');
                }
            }).fail(function() {
                showAlert('danger', 'An error occurred. Please try again.');
            });
            <?php else: ?>
            showAlert('warning', 'Please login to add events to cart');
            setTimeout(function() {
                window.location.href = '/auth/login.php';
            }, 2000);
            <?php endif; ?>
        }

        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Remove existing alerts
            $('.alert').remove();

            // Add new alert at the top of the page
            $('main').prepend(alertHtml);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        // Form validation
        function validateForm(formId) {
            const form = document.getElementById(formId);
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // Search functionality
        function performSearch() {
            const searchTerm = document.getElementById('searchTerm').value;
            const location = document.getElementById('searchLocation').value;
            const date = document.getElementById('searchDate').value;

            const params = new URLSearchParams();
            if (searchTerm) params.append('search', searchTerm);
            if (location) params.append('location', location);
            if (date) params.append('date', date);

            window.location.href = '<?php echo SITE_URL; ?>/events/search.php?' + params.toString();
        }
    </script>
</body>
</html>
