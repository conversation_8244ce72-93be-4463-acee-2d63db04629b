// Main JavaScript file for Event Booking System

// Global variables
let isLoading = false;

// Document ready with error handling
$(document).ready(function() {
    try {
        // Initialize tooltips
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Initialize popovers
        if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert:not(.alert-permanent)').fadeOut();
    }, 5000);

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Form validation enhancement
    $('form').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        if (!submitBtn.hasClass('no-loading')) {
            showLoading(submitBtn);
        }
    });

    // Auto-resize textareas
    $('textarea').each(function() {
        this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
    }).on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Initialize lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

        // Update cart count on page load
        updateCartCount();
    } catch (error) {
        console.error('Error initializing page:', error);
        // Fallback: still try to update cart count
        try {
            updateCartCount();
        } catch (cartError) {
            console.error('Error updating cart count:', cartError);
        }
    }
});

// Utility Functions
function showLoading(element) {
    if (element) {
        element.prop('disabled', true);
        const originalText = element.html();
        element.data('original-text', originalText);
        element.html('<span class="spinner"></span> Loading...');
    }
    isLoading = true;
}

function hideLoading(element) {
    if (element && element.data('original-text')) {
        element.prop('disabled', false);
        element.html(element.data('original-text'));
    }
    isLoading = false;
}

function showAlert(type, message, permanent = false) {
    const alertClass = permanent ? 'alert-permanent' : '';
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show ${alertClass}" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts if not permanent
    if (!permanent) {
        $('.alert:not(.alert-permanent)').remove();
    }

    // Add new alert at the top of the main content
    $('main').prepend(alertHtml);

    // Auto-dismiss after 5 seconds if not permanent
    if (!permanent) {
        setTimeout(function() {
            $('.alert:not(.alert-permanent)').fadeOut();
        }, 5000);
    }

    // Scroll to top to show alert
    $('html, body').animate({ scrollTop: 0 }, 300);
}

// Enhanced confirmation dialog
function confirmAction(title, message, confirmText = 'Confirm', confirmClass = 'btn-primary', onConfirm = null, onCancel = null) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('confirmModal');
    if (!modal) {
        modal = createConfirmModal();
    }

    // Update modal content
    document.getElementById('confirmModalTitle').textContent = title;
    document.getElementById('confirmModalBody').textContent = message;

    const confirmBtn = document.getElementById('confirmModalConfirm');
    confirmBtn.textContent = confirmText;
    confirmBtn.className = `btn ${confirmClass}`;

    // Set up event handlers
    confirmBtn.onclick = function() {
        if (onConfirm) onConfirm();
        bootstrap.Modal.getInstance(modal).hide();
    };

    document.getElementById('confirmModalCancel').onclick = function() {
        if (onCancel) onCancel();
        bootstrap.Modal.getInstance(modal).hide();
    };

    // Show modal
    new bootstrap.Modal(modal).show();
}

// Create confirmation modal
function createConfirmModal() {
    const modalHTML = `
        <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalTitle" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="confirmModalTitle">Confirm Action</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p id="confirmModalBody">Are you sure you want to proceed?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" id="confirmModalCancel" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="confirmModalConfirm">Confirm</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    return document.getElementById('confirmModal');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-CF', {
        style: 'currency',
        currency: 'XAF',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function formatTime(timeString) {
    const time = new Date('2000-01-01 ' + timeString);
    return time.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Cart Functions
function updateCartCount() {
    $.get('/booking/get_cart_count.php', function(data) {
        if (data && typeof data.count !== 'undefined') {
            $('#cartCount').text(data.count);
            if (data.count > 0) {
                $('#cartCount').show();
            } else {
                $('#cartCount').hide();
            }
        }
    }).fail(function() {
        console.log('Failed to update cart count');
    });
}

function addToCart(eventId, quantity = 1) {
    if (isLoading) return;

    const csrfToken = $('meta[name="csrf-token"]').attr('content') ||
                     $('input[name="csrf_token"]').val();

    if (!csrfToken) {
        showAlert('danger', 'Security token not found. Please refresh the page.');
        return;
    }

    showLoading();

    $.post('/booking/add_to_cart.php', {
        event_id: eventId,
        quantity: quantity,
        csrf_token: csrfToken
    }, function(response) {
        hideLoading();
        if (response && response.success) {
            updateCartCount();
            showAlert('success', response.message || 'Event added to cart successfully!');
        } else {
            showAlert('danger', response.message || 'Failed to add event to cart');
        }
    }, 'json').fail(function(xhr, status, error) {
        hideLoading();
        console.error('Add to cart error:', error);
        if (xhr.status === 401) {
            showAlert('warning', 'Please login to add events to cart');
            setTimeout(function() {
                window.location.href = '/auth/login.php';
            }, 2000);
        } else {
            showAlert('danger', 'An error occurred. Please try again.');
        }
    });
}

// Search Functions
function performSearch() {
    const searchTerm = $('#searchTerm').val();
    const location = $('#searchLocation').val();
    const date = $('#searchDate').val();

    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (location) params.append('location', location);
    if (date) params.append('date', date);

    window.location.href = '/events/search.php?' + params.toString();
}

// Enhanced Form Validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;

    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    let firstInvalidField = null;

    // Clear previous validation states
    form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());

    inputs.forEach(input => {
        const fieldName = input.getAttribute('data-field-name') ||
                         input.getAttribute('placeholder') ||
                         input.name || input.id;
        let errorMessage = '';

        // Required field validation
        if (!input.value.trim()) {
            errorMessage = `${fieldName} is required.`;
            isValid = false;
            if (!firstInvalidField) firstInvalidField = input;
        } else {
            // Type-specific validation
            switch (input.type) {
                case 'email':
                    if (!isValidEmail(input.value)) {
                        errorMessage = 'Please enter a valid email address.';
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = input;
                    }
                    break;
                case 'password':
                    if (input.value.length < 6) {
                        errorMessage = 'Password must be at least 6 characters long.';
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = input;
                    }
                    break;
                case 'tel':
                    if (input.value && !isValidPhone(input.value)) {
                        errorMessage = 'Please enter a valid phone number.';
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = input;
                    }
                    break;
                case 'number':
                    const min = input.getAttribute('min');
                    const max = input.getAttribute('max');
                    const value = parseFloat(input.value);
                    if (min && value < parseFloat(min)) {
                        errorMessage = `Value must be at least ${min}.`;
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = input;
                    } else if (max && value > parseFloat(max)) {
                        errorMessage = `Value must not exceed ${max}.`;
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = input;
                    }
                    break;
            }

            // Minimum length validation
            const minLength = input.getAttribute('minlength');
            if (minLength && input.value.length < parseInt(minLength)) {
                errorMessage = `Must be at least ${minLength} characters long.`;
                isValid = false;
                if (!firstInvalidField) firstInvalidField = input;
            }
        }

        // Add validation feedback
        if (errorMessage) {
            input.classList.add('is-invalid');
            addValidationFeedback(input, errorMessage);
        } else {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        }
    });

    // Password confirmation validation
    const passwordField = form.querySelector('input[name="password"]');
    const confirmPasswordField = form.querySelector('input[name="confirm_password"]');
    if (passwordField && confirmPasswordField && confirmPasswordField.value) {
        if (passwordField.value !== confirmPasswordField.value) {
            confirmPasswordField.classList.add('is-invalid');
            addValidationFeedback(confirmPasswordField, 'Passwords do not match.');
            isValid = false;
            if (!firstInvalidField) firstInvalidField = confirmPasswordField;
        }
    }

    // Focus on first invalid field
    if (firstInvalidField) {
        firstInvalidField.focus();
        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    return isValid;
}

// Helper function to add validation feedback
function addValidationFeedback(input, message) {
    const feedback = document.createElement('div');
    feedback.className = 'invalid-feedback';
    feedback.textContent = message;
    input.parentNode.appendChild(feedback);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Real-time validation
function setupRealTimeValidation(formId) {
    const form = document.getElementById(formId);
    if (!form) return;

    const inputs = form.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        // Validate on blur
        input.addEventListener('blur', function() {
            validateSingleField(this);
        });

        // Clear validation on focus
        input.addEventListener('focus', function() {
            this.classList.remove('is-invalid', 'is-valid');
            const feedback = this.parentNode.querySelector('.invalid-feedback');
            if (feedback) feedback.remove();
        });

        // Real-time password confirmation
        if (input.name === 'confirm_password') {
            input.addEventListener('input', function() {
                const passwordField = form.querySelector('input[name="password"]');
                if (passwordField && this.value) {
                    if (passwordField.value !== this.value) {
                        this.classList.add('is-invalid');
                        this.classList.remove('is-valid');
                    } else {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    }
                }
            });
        }
    });
}

// Validate single field
function validateSingleField(input) {
    const fieldName = input.getAttribute('data-field-name') ||
                     input.getAttribute('placeholder') ||
                     input.name || input.id;
    let errorMessage = '';

    // Clear previous feedback
    input.classList.remove('is-invalid', 'is-valid');
    const existingFeedback = input.parentNode.querySelector('.invalid-feedback');
    if (existingFeedback) existingFeedback.remove();

    // Skip validation if field is not required and empty
    if (!input.hasAttribute('required') && !input.value.trim()) {
        return true;
    }

    // Required field validation
    if (input.hasAttribute('required') && !input.value.trim()) {
        errorMessage = `${fieldName} is required.`;
    } else if (input.value.trim()) {
        // Type-specific validation
        switch (input.type) {
            case 'email':
                if (!isValidEmail(input.value)) {
                    errorMessage = 'Please enter a valid email address.';
                }
                break;
            case 'password':
                if (input.value.length < 6) {
                    errorMessage = 'Password must be at least 6 characters long.';
                }
                break;
            case 'tel':
                if (!isValidPhone(input.value)) {
                    errorMessage = 'Please enter a valid phone number.';
                }
                break;
        }

        // Minimum length validation
        const minLength = input.getAttribute('minlength');
        if (minLength && input.value.length < parseInt(minLength)) {
            errorMessage = `Must be at least ${minLength} characters long.`;
        }
    }

    // Add validation feedback
    if (errorMessage) {
        input.classList.add('is-invalid');
        addValidationFeedback(input, errorMessage);
        return false;
    } else {
        input.classList.add('is-valid');
        return true;
    }
}

// Image Functions
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#' + previewId).attr('src', e.target.result).show();
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Local Storage Functions
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (e) {
        console.error('Failed to save to localStorage:', e);
        return false;
    }
}

function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.error('Failed to get from localStorage:', e);
        return null;
    }
}

function removeFromLocalStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (e) {
        console.error('Failed to remove from localStorage:', e);
        return false;
    }
}

// Debounce function for search inputs
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// Initialize search with debounce
const debouncedSearch = debounce(function() {
    performSearch();
}, 500);

// Bind search inputs
$(document).on('input', '#searchTerm, #searchLocation', debouncedSearch);
$(document).on('change', '#searchDate', performSearch);

// Handle back button
window.addEventListener('popstate', function(event) {
    if (event.state && event.state.page) {
        // Handle page state if needed
        console.log('Back button pressed');
    }
});

// Error handling for AJAX requests
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    console.error('AJAX Error:', thrownError);
    if (xhr.status === 401) {
        showAlert('warning', 'Your session has expired. Please log in again.');
        setTimeout(function() {
            window.location.href = '/auth/login.php';
        }, 2000);
    } else if (xhr.status === 403) {
        showAlert('danger', 'Access denied.');
    } else if (xhr.status >= 500) {
        showAlert('danger', 'Server error. Please try again later.');
    }
});

// Keyboard shortcuts
$(document).keydown(function(e) {
    // Ctrl+/ or Cmd+/ to focus search
    if ((e.ctrlKey || e.metaKey) && e.which === 191) {
        e.preventDefault();
        $('#searchTerm').focus();
    }

    // Escape to close modals
    if (e.which === 27) {
        $('.modal').modal('hide');
    }
});

// Export functions for global use
window.EventBooking = {
    showAlert,
    showLoading,
    hideLoading,
    updateCartCount,
    addToCart,
    performSearch,
    validateForm,
    formatCurrency,
    formatDate,
    formatTime,
    confirmAction,
    saveToLocalStorage,
    getFromLocalStorage,
    removeFromLocalStorage
};
