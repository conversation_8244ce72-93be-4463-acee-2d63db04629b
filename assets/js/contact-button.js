// Contact Me Button Functionality
class ContactButton {
    constructor() {
        this.init();
    }

    init() {
        this.createContactButton();
        this.createContactModal();
        this.bindEvents();
    }

    createContactButton() {
        // Create the floating contact button
        const contactBtn = document.createElement('button');
        contactBtn.className = 'contact-me-btn';
        contactBtn.innerHTML = '<i class="fas fa-comments"></i>Contact Me';
        contactBtn.id = 'contactMeBtn';
        
        document.body.appendChild(contactBtn);
    }

    createContactModal() {
        // Create the contact modal
        const modal = document.createElement('div');
        modal.className = 'contact-modal';
        modal.id = 'contactModal';
        
        modal.innerHTML = `
            <div class="contact-modal-content">
                <div class="contact-modal-header">
                    <button class="contact-modal-close" id="closeContactModal">
                        <i class="fas fa-times"></i>
                    </button>
                    <h4><i class="fas fa-user-circle me-2"></i>Contact <PERSON></h4>
                    <p class="mb-0">Get in touch with me through any of these channels</p>
                </div>
                <div class="contact-modal-body">
                    ${this.getContactOptions()}
                    ${this.getQuickActions()}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    getContactOptions() {
        return `
            <div class="contact-options">
                <!-- Email Option -->
                <a href="mailto:<EMAIL>?subject=Event Booking Inquiry" class="contact-option" data-contact="email">
                    <div class="contact-option-icon email">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-option-content">
                        <h6>Email Me</h6>
                        <p class="contact-detail"><EMAIL></p>
                        <p>Send me an email directly</p>
                    </div>
                </a>

                <!-- Phone Option -->
                <a href="tel:+237651408682" class="contact-option" data-contact="phone">
                    <div class="contact-option-icon phone">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="contact-option-content">
                        <h6>Call Me</h6>
                        <p class="contact-detail">+237 651 408 682</p>
                        <p>Call me directly (Cameroon)</p>
                    </div>
                </a>

                <!-- WhatsApp Option -->
                <a href="https://wa.me/237651408682?text=Hello%20Fritz,%20I%20have%20a%20question%20about%20event%20booking" target="_blank" class="contact-option" data-contact="whatsapp">
                    <div class="contact-option-icon whatsapp">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="contact-option-content">
                        <h6>WhatsApp</h6>
                        <p class="contact-detail">+237 651 408 682</p>
                        <p>Message me on WhatsApp</p>
                    </div>
                </a>

                <!-- Telegram Option -->
                <a href="https://t.me/+237651408682" target="_blank" class="contact-option" data-contact="telegram">
                    <div class="contact-option-icon telegram">
                        <i class="fab fa-telegram"></i>
                    </div>
                    <div class="contact-option-content">
                        <h6>Telegram</h6>
                        <p class="contact-detail">@fritztayong</p>
                        <p>Chat with me on Telegram</p>
                    </div>
                </a>

                <!-- Contact Form Option -->
                <a href="/contact.php" class="contact-option" data-contact="form">
                    <div class="contact-option-icon form">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="contact-option-content">
                        <h6>Contact Form</h6>
                        <p class="contact-detail">Online Form</p>
                        <p>Fill out our contact form</p>
                    </div>
                </a>

                <!-- Location Option -->
                <div class="contact-option" data-contact="location">
                    <div class="contact-option-icon location">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="contact-option-content">
                        <h6>Location</h6>
                        <p class="contact-detail">Central Africa</p>
                        <p>Serving Cameroon & neighboring countries</p>
                    </div>
                </div>
            </div>
        `;
    }

    getQuickActions() {
        return `
            <div class="quick-actions">
                <a href="mailto:<EMAIL>?subject=Event Booking Inquiry&body=Hello Fritz,%0D%0A%0D%0AI am interested in your event booking services.%0D%0A%0D%0APlease contact me at your earliest convenience.%0D%0A%0D%0AThank you!" class="quick-action-btn primary">
                    <i class="fas fa-envelope me-1"></i>Quick Email
                </a>
                <a href="https://wa.me/237651408682?text=Hello%20Fritz,%20I%20need%20help%20with%20event%20booking" target="_blank" class="quick-action-btn success">
                    <i class="fab fa-whatsapp me-1"></i>Quick WhatsApp
                </a>
            </div>
        `;
    }

    bindEvents() {
        // Open modal
        document.getElementById('contactMeBtn').addEventListener('click', () => {
            this.openModal();
        });

        // Close modal
        document.getElementById('closeContactModal').addEventListener('click', () => {
            this.closeModal();
        });

        // Close modal when clicking outside
        document.getElementById('contactModal').addEventListener('click', (e) => {
            if (e.target.id === 'contactModal') {
                this.closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });

        // Track contact option clicks
        document.querySelectorAll('.contact-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const contactType = e.currentTarget.getAttribute('data-contact');
                this.trackContactClick(contactType);
            });
        });
    }

    openModal() {
        const modal = document.getElementById('contactModal');
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Add analytics tracking
        this.trackEvent('contact_modal_opened');
    }

    closeModal() {
        const modal = document.getElementById('contactModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        
        // Add analytics tracking
        this.trackEvent('contact_modal_closed');
    }

    trackContactClick(contactType) {
        // Track which contact method was used
        console.log(`Contact method used: ${contactType}`);
        
        // You can add analytics tracking here
        this.trackEvent('contact_method_clicked', { method: contactType });
        
        // Show success message for some actions
        if (contactType === 'email' || contactType === 'phone') {
            setTimeout(() => {
                this.showNotification(`Opening ${contactType} application...`, 'info');
            }, 100);
        }
    }

    trackEvent(eventName, data = {}) {
        // Basic event tracking - you can integrate with Google Analytics, etc.
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, data);
        }
        
        // Console log for debugging
        console.log('Event tracked:', eventName, data);
    }

    showNotification(message, type = 'info') {
        // Create a simple notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 1070;
            min-width: 300px;
            animation: slideInRight 0.3s ease;
        `;
        notification.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }

    // Utility method to copy text to clipboard
    copyToClipboard(text, successMessage = 'Copied to clipboard!') {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showNotification(successMessage, 'success');
            }).catch(() => {
                this.fallbackCopyTextToClipboard(text, successMessage);
            });
        } else {
            this.fallbackCopyTextToClipboard(text, successMessage);
        }
    }

    fallbackCopyTextToClipboard(text, successMessage) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showNotification(successMessage, 'success');
        } catch (err) {
            this.showNotification('Failed to copy to clipboard', 'danger');
        }
        
        document.body.removeChild(textArea);
    }
}

// Initialize the contact button when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new ContactButton();
});

// Add CSS animation for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
