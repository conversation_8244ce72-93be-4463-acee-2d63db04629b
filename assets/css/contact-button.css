/* Contact Me <PERSON> Styles */
.contact-me-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1050;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.contact-me-btn:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 123, 255, 0.6);
    color: white;
}

.contact-me-btn i {
    margin-right: 8px;
    font-size: 18px;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 20px rgba(0, 123, 255, 0.4);
    }
    50% {
        box-shadow: 0 4px 20px rgba(0, 123, 255, 0.8);
    }
    100% {
        box-shadow: 0 4px 20px rgba(0, 123, 255, 0.4);
    }
}

/* Contact Modal Styles */
.contact-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1060;
    animation: fadeIn 0.3s ease;
}

.contact-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-modal-content {
    background: white;
    border-radius: 15px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

.contact-modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
    text-align: center;
    position: relative;
}

.contact-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.contact-modal-close:hover {
    opacity: 1;
}

.contact-modal-body {
    padding: 30px;
}

.contact-option {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    cursor: pointer;
}

.contact-option:hover {
    border-color: #007bff;
    background: #f8f9ff;
    color: #007bff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.contact-option-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
}

.contact-option-icon.email {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.contact-option-icon.phone {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.contact-option-icon.whatsapp {
    background: linear-gradient(135deg, #25d366, #128c7e);
}

.contact-option-icon.telegram {
    background: linear-gradient(135deg, #0088cc, #006699);
}

.contact-option-icon.form {
    background: linear-gradient(135deg, #6f42c1, #5a2d91);
}

.contact-option-icon.location {
    background: linear-gradient(135deg, #fd7e14, #e55a00);
}

.contact-option-content h6 {
    margin: 0 0 5px 0;
    font-weight: 600;
    font-size: 16px;
}

.contact-option-content p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.contact-option-content .contact-detail {
    font-weight: 500;
    color: #333;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.quick-action-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 14px;
}

.quick-action-btn.primary {
    background: #007bff;
    color: white;
}

.quick-action-btn.primary:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
}

.quick-action-btn.success {
    background: #28a745;
    color: white;
}

.quick-action-btn.success:hover {
    background: #1e7e34;
    color: white;
    text-decoration: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .contact-me-btn {
        bottom: 20px;
        right: 20px;
        padding: 12px 20px;
        font-size: 14px;
    }
    
    .contact-modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .contact-modal-body {
        padding: 20px;
    }
    
    .contact-option {
        padding: 12px;
    }
    
    .contact-option-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .quick-actions {
        flex-direction: column;
    }
}

/* Hide on very small screens */
@media (max-width: 480px) {
    .contact-me-btn {
        padding: 10px 15px;
        font-size: 12px;
    }
    
    .contact-me-btn i {
        margin-right: 5px;
        font-size: 14px;
    }
}
