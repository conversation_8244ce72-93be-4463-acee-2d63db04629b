<?php
// Simple Gmail App Password Setup Script
// This script helps you add your Gmail App Password directly

echo "🔧 Gmail App Password Setup\n";
echo "============================\n\n";

// Get the Gmail App Password from user input
echo "Please enter your Gmail App Password (16 characters, letters and numbers only):\n";
echo "Example: abcdefghijklmnop or abcd efgh ijkl mnop\n";
echo "Password: ";

// Read password from command line
$handle = fopen("php://stdin", "r");
$appPassword = trim(fgets($handle));
fclose($handle);

// Clean the password (remove spaces and special characters)
$cleanPassword = preg_replace('/[^a-zA-Z0-9]/', '', $appPassword);

echo "\nProcessing...\n";

// Validate password
if (empty($cleanPassword)) {
    echo "❌ Error: No password entered.\n";
    exit(1);
}

if (strlen($cleanPassword) !== 16) {
    echo "❌ Error: Password must be exactly 16 characters. You entered " . strlen($cleanPassword) . " characters.\n";
    echo "Clean password: '$cleanPassword'\n";
    exit(1);
}

if (!ctype_alnum($cleanPassword)) {
    echo "❌ Error: Password must contain only letters and numbers.\n";
    exit(1);
}

// Read the config file
$configFile = 'includes/config.php';
if (!file_exists($configFile)) {
    echo "❌ Error: Config file not found at $configFile\n";
    exit(1);
}

$configContent = file_get_contents($configFile);
if ($configContent === false) {
    echo "❌ Error: Could not read config file.\n";
    exit(1);
}

// Replace the empty password
$oldLine = "define('SMTP_PASSWORD', '');";
$newLine = "define('SMTP_PASSWORD', '$cleanPassword');";

$newContent = str_replace($oldLine, $newLine, $configContent);

if ($newContent === $configContent) {
    echo "⚠️  Warning: Password may already be set, or config format has changed.\n";
    echo "Looking for line: $oldLine\n";
    
    // Check if password is already set
    if (strpos($configContent, "define('SMTP_PASSWORD', '$cleanPassword');") !== false) {
        echo "✅ Password is already correctly set!\n";
        exit(0);
    } else {
        echo "❌ Could not find the line to replace. Please check the config file manually.\n";
        exit(1);
    }
}

// Write the updated content back
if (file_put_contents($configFile, $newContent)) {
    echo "✅ Success! Gmail App Password has been configured.\n";
    echo "Password set: " . substr($cleanPassword, 0, 4) . "****" . substr($cleanPassword, -4) . "\n";
    echo "\nNext steps:\n";
    echo "1. Visit: http://localhost:8000/test-email.php\n";
    echo "2. Send a test email to verify it works\n";
    echo "3. Try the contact form at: http://localhost:8000/contact.php\n";
} else {
    echo "❌ Error: Could not write to config file. Check file permissions.\n";
    exit(1);
}

echo "\n🎉 Setup complete! Your email system is ready.\n";
?>
