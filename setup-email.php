<?php
// Email Configuration Helper
// This script helps you set up the Gmail App Password for email functionality

$configFile = 'includes/config.php';
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['app_password'])) {
    $appPassword = trim($_POST['app_password']);

    if (empty($appPassword)) {
        $message = 'Please enter your Gmail App Password.';
        $messageType = 'danger';
    } else {
        // Clean the password (remove spaces and validate)
        $cleanPassword = str_replace(' ', '', $appPassword);
        if (strlen($cleanPassword) !== 16 || !ctype_alnum($cleanPassword)) {
            $message = 'Gmail App Password should be 16 characters (letters and numbers only). Current length: ' . strlen($cleanPassword);
            $messageType = 'warning';
        } else {
            // Read the config file
            $configContent = file_get_contents($configFile);

            if ($configContent === false) {
                $message = 'Could not read the configuration file.';
                $messageType = 'danger';
            } else {
                // Replace the empty password with the provided one (use cleaned password)
                $oldLine = "define('SMTP_PASSWORD', '');";
                $newLine = "define('SMTP_PASSWORD', '" . $cleanPassword . "');";

                $newContent = str_replace($oldLine, $newLine, $configContent);

                if ($newContent === $configContent) {
                    $message = 'App Password may already be set, or configuration format has changed.';
                    $messageType = 'warning';
                } else {
                    // Write the updated content back
                    if (file_put_contents($configFile, $newContent)) {
                        $message = 'Gmail App Password has been successfully configured! You can now test email functionality.';
                        $messageType = 'success';
                    } else {
                        $message = 'Could not write to the configuration file. Please check file permissions.';
                        $messageType = 'danger';
                    }
                }
            }
        }
    }
}

// Check current configuration
$currentConfig = [];
if (file_exists($configFile)) {
    $configContent = file_get_contents($configFile);

    // Extract current settings
    preg_match("/define\('SMTP_HOST', '(.+?)'\);/", $configContent, $matches);
    $currentConfig['host'] = $matches[1] ?? 'Not set';

    preg_match("/define\('SMTP_USERNAME', '(.+?)'\);/", $configContent, $matches);
    $currentConfig['username'] = $matches[1] ?? 'Not set';

    preg_match("/define\('SMTP_PASSWORD', '(.+?)'\);/", $configContent, $matches);
    $currentConfig['password_set'] = !empty($matches[1]);

    preg_match("/define\('ADMIN_EMAIL', '(.+?)'\);/", $configContent, $matches);
    $currentConfig['admin_email'] = $matches[1] ?? 'Not set';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Setup - Event Booking System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .step-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        .config-status {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        .password-input {
            font-family: 'Courier New', monospace;
            letter-spacing: 2px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="setup-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-envelope-circle-check text-primary me-2"></i>Email Setup</h1>
            <p class="text-muted">Configure Gmail App Password for customer contact messages</p>
        </div>

        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Current Configuration Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Current Configuration</h5>
            </div>
            <div class="card-body">
                <div class="config-status">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>SMTP Host:</strong> <?php echo htmlspecialchars($currentConfig['host'] ?? 'Not configured'); ?></p>
                            <p><strong>Username:</strong> <?php echo htmlspecialchars($currentConfig['username'] ?? 'Not configured'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Admin Email:</strong> <?php echo htmlspecialchars($currentConfig['admin_email'] ?? 'Not configured'); ?></p>
                            <p><strong>App Password:</strong>
                                <?php if ($currentConfig['password_set'] ?? false): ?>
                                    <span class="text-success"><i class="fas fa-check-circle me-1"></i>Configured</span>
                                <?php else: ?>
                                    <span class="text-danger"><i class="fas fa-times-circle me-1"></i>Not Set</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Setup Steps -->
        <div class="row">
            <div class="col-md-6">
                <div class="card step-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Step 1: Enable 2FA</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-3">
                            <li>Go to <a href="https://myaccount.google.com/" target="_blank">Google Account</a></li>
                            <li>Click <strong>Security</strong></li>
                            <li>Enable <strong>2-Step Verification</strong></li>
                        </ol>
                        <a href="https://myaccount.google.com/security" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt me-1"></i>Open Google Security
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card step-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key me-2"></i>Step 2: Generate App Password</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-3">
                            <li>In Security settings, click <strong>App passwords</strong></li>
                            <li>Select <strong>Mail</strong> app</li>
                            <li>Select <strong>Other (Custom name)</strong></li>
                            <li>Enter: <strong>Event Booking System</strong></li>
                            <li>Click <strong>Generate</strong></li>
                            <li>Copy the 16-character password</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- App Password Configuration -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-wrench me-2"></i>Step 3: Configure App Password</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="app_password" class="form-label">Gmail App Password</label>
                        <input type="text" class="form-control password-input" id="app_password" name="app_password"
                               placeholder="abcd efgh ijkl mnop" maxlength="19" required>
                        <div class="form-text">
                            Enter the 16-character App Password from Google (spaces are optional)
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Important:</strong> Use the App Password, NOT your regular Gmail password.
                        The App Password is specifically generated for this application.
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save App Password
                    </button>
                </form>
            </div>
        </div>

        <!-- Test Email -->
        <?php if ($currentConfig['password_set'] ?? false): ?>
        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-paper-plane me-2"></i>Step 4: Test Email</h5>
            </div>
            <div class="card-body">
                <p>Your email configuration is complete! Now test the functionality:</p>
                <div class="d-grid gap-2 d-md-flex">
                    <a href="test-email.php" class="btn btn-success">
                        <i class="fas fa-vial me-2"></i>Test Email Sending
                    </a>
                    <a href="contact.php" class="btn btn-outline-success">
                        <i class="fas fa-envelope me-2"></i>Try Contact Form
                    </a>
                    <a href="index.php" class="btn btn-outline-primary">
                        <i class="fas fa-home me-2"></i>Back to Website
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Help Section -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>Need Help?</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Common Issues:</h6>
                        <ul class="small">
                            <li><strong>"Could not authenticate"</strong> - Wrong App Password</li>
                            <li><strong>"2FA required"</strong> - Enable 2-factor authentication first</li>
                            <li><strong>"App passwords not available"</strong> - 2FA not enabled</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Contact Support:</h6>
                        <p class="small">
                            <i class="fas fa-phone me-1"></i>+237 651 408 682<br>
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp: +237 651 408 682<br>
                            <i class="fas fa-envelope me-1"></i><EMAIL>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Format app password input
        document.getElementById('app_password').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^a-zA-Z0-9]/g, ''); // Remove non-alphanumeric characters
            if (value.length > 16) {
                value = value.substring(0, 16);
            }

            // Add spaces every 4 characters for readability
            let formatted = value.replace(/(.{4})/g, '$1 ').trim();
            e.target.value = formatted;

            // Update character count
            const remaining = 16 - value.length;
            const helpText = e.target.nextElementSibling;
            if (helpText) {
                helpText.innerHTML = `Enter the 16-character App Password from Google (${value.length}/16 characters)`;
                helpText.className = value.length === 16 ? 'form-text text-success' : 'form-text';
            }
        });
    </script>
</body>
</html>
